import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, CheckCircle, <PERSON>rk<PERSON> } from "lucide-react"

export default function MobileScreen8_2() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Account Created</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8 flex flex-col items-center justify-center">
        {/* Success Icon */}
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-8">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Welcome to GiftSpark AI!</h1>

        {/* Description */}
        <p className="text-gray-600 text-center mb-8 leading-relaxed">
          Your account has been successfully created. You're now ready to discover amazing gift recommendations powered
          by AI.
        </p>

        {/* Features Preview */}
        <div className="w-full space-y-4 mb-8">
          <div className="flex items-center gap-3 p-4 bg-orange-50 rounded-xl">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">AI-Powered Recommendations</h3>
              <p className="text-sm text-gray-600">Get personalized gift suggestions</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-xl">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 text-lg">🎁</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Curated Collections</h3>
              <p className="text-sm text-gray-600">Explore themed gift collections</p>
            </div>
          </div>

          <div className="flex items-center gap-3 p-4 bg-purple-50 rounded-xl">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
              <span className="text-purple-600 text-lg">📅</span>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Date Reminders</h3>
              <p className="text-sm text-gray-600">Never miss important occasions</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="w-full space-y-3">
          <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold">
            Start Finding Gifts
          </Button>
          <Button variant="outline" className="w-full h-12">
            Complete Profile Setup
          </Button>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
