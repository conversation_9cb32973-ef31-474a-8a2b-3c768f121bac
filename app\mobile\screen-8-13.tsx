import { But<PERSON> } from "@/components/ui/button"
import { Arrow<PERSON><PERSON>t, Star, ThumbsUp, MessageSquare, Share } from "lucide-react"

export default function MobileScreen8_13() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Rate GiftSpark AI</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8 flex flex-col items-center">
        {/* App Icon */}
        <div className="w-20 h-20 bg-gradient-to-br from-orange-400 to-pink-500 rounded-2xl flex items-center justify-center mb-6">
          <span className="text-white text-2xl">🎁</span>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Enjoying GiftSpark AI?</h1>
        <p className="text-gray-600 text-center mb-8 leading-relaxed">
          Your feedback helps us improve and create better gift recommendations for everyone.
        </p>

        {/* Star Rating */}
        <div className="mb-8">
          <p className="text-center text-gray-700 mb-4">How would you rate your experience?</p>
          <div className="flex justify-center gap-2">
            {[1, 2, 3, 4, 5].map((star) => (
              <button key={star} className="p-2">
                <Star className="w-8 h-8 text-yellow-400 fill-current" />
              </button>
            ))}
          </div>
        </div>

        {/* Feedback Options */}
        <div className="w-full space-y-4 mb-8">
          <div className="bg-green-50 rounded-xl p-4 border border-green-100">
            <div className="flex items-center gap-3 mb-3">
              <ThumbsUp className="w-5 h-5 text-green-600" />
              <h3 className="font-medium text-green-900">What do you love most?</h3>
            </div>
            <div className="flex flex-wrap gap-2">
              {["AI Recommendations", "Easy to Use", "Great Design", "Helpful Features"].map((tag) => (
                <button
                  key={tag}
                  className="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full hover:bg-green-200"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>

          <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
            <div className="flex items-center gap-3 mb-3">
              <MessageSquare className="w-5 h-5 text-blue-600" />
              <h3 className="font-medium text-blue-900">Any suggestions?</h3>
            </div>
            <textarea
              placeholder="Tell us how we can improve..."
              className="w-full h-20 p-3 bg-white border border-blue-200 rounded-lg text-sm resize-none"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="w-full space-y-3">
          <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold">
            <Star className="w-5 h-5 mr-2" />
            Rate on App Store
          </Button>
          <Button variant="outline" className="w-full h-12">
            <Share className="w-5 h-5 mr-2" />
            Share with Friends
          </Button>
          <Button variant="ghost" className="w-full text-gray-500">
            Maybe Later
          </Button>
        </div>

        {/* Thank You Note */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Thank you for using GiftSpark AI! Your feedback means the world to us. 💝
          </p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
