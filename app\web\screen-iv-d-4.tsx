"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Activity,
  Cpu,
  HardDrive,
  MemoryStick,
  Network,
  Server,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
} from "lucide-react"

export default function SystemMonitoringScreen() {
  const [refreshInterval, setRefreshInterval] = useState("5")

  const systemMetrics = {
    cpu: { usage: 45, cores: 8, temperature: 68 },
    memory: { used: 6.2, total: 16, percentage: 39 },
    disk: { used: 128, total: 500, percentage: 26 },
    network: { inbound: 125, outbound: 89 },
  }

  const services = [
    { name: "API Server", status: "running", uptime: "99.9%", port: 3000 },
    { name: "Database", status: "running", uptime: "99.8%", port: 5432 },
    { name: "Redis Cache", status: "running", uptime: "99.9%", port: 6379 },
    { name: "Task Queue", status: "running", uptime: "99.7%", port: 4000 },
    { name: "File Storage", status: "warning", uptime: "98.5%", port: 9000 },
    { name: "Search Engine", status: "running", uptime: "99.6%", port: 9200 },
  ]

  const alerts = [
    { id: 1, type: "warning", message: "High disk usage on /var/log partition", time: "2 minutes ago" },
    { id: 2, type: "info", message: "Scheduled backup completed successfully", time: "1 hour ago" },
    { id: 3, type: "warning", message: "Unusual traffic spike detected", time: "3 hours ago" },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">System Monitoring</h1>
          <p className="text-gray-600">Real-time system performance and health monitoring</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="refresh">Auto Refresh (seconds):</Label>
            <Input
              id="refresh"
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(e.target.value)}
              className="w-20"
              type="number"
            />
          </div>
          <Button variant="outline">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {/* CPU Usage */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.cpu.usage}%</div>
                <Progress value={systemMetrics.cpu.usage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  {systemMetrics.cpu.cores} cores • {systemMetrics.cpu.temperature}°C
                </p>
              </CardContent>
            </Card>

            {/* Memory Usage */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
                <MemoryStick className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.memory.percentage}%</div>
                <Progress value={systemMetrics.memory.percentage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  {systemMetrics.memory.used}GB / {systemMetrics.memory.total}GB
                </p>
              </CardContent>
            </Card>

            {/* Disk Usage */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
                <HardDrive className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemMetrics.disk.percentage}%</div>
                <Progress value={systemMetrics.disk.percentage} className="mt-2" />
                <p className="text-xs text-muted-foreground mt-2">
                  {systemMetrics.disk.used}GB / {systemMetrics.disk.total}GB
                </p>
              </CardContent>
            </Card>

            {/* Network Traffic */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Network Traffic</CardTitle>
                <Network className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>In:</span>
                    <span className="font-medium">{systemMetrics.network.inbound} MB/s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Out:</span>
                    <span className="font-medium">{systemMetrics.network.outbound} MB/s</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Services Status */}
          <Card>
            <CardHeader>
              <CardTitle>Services Status</CardTitle>
              <CardDescription>Current status of all system services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {services.map((service) => (
                  <div key={service.name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          service.status === "running"
                            ? "bg-green-500"
                            : service.status === "warning"
                              ? "bg-yellow-500"
                              : "bg-red-500"
                        }`}
                      />
                      <div>
                        <p className="font-medium">{service.name}</p>
                        <p className="text-sm text-gray-500">Port {service.port}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant={service.status === "running" ? "default" : "secondary"}>{service.status}</Badge>
                      <p className="text-xs text-gray-500 mt-1">{service.uptime}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Response Time Trends</CardTitle>
                <CardDescription>Average response time over the last 24 hours</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <TrendingUp className="h-8 w-8 mr-2" />
                  Performance Chart Placeholder
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Usage History</CardTitle>
                <CardDescription>CPU, Memory, and Disk usage trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <Activity className="h-8 w-8 mr-2" />
                  Resource Chart Placeholder
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>Detailed performance statistics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <h4 className="font-medium">API Performance</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Avg Response Time:</span>
                      <span>245ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Requests/sec:</span>
                      <span>127</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Error Rate:</span>
                      <span>0.02%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">Database Performance</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Query Time:</span>
                      <span>12ms</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Connections:</span>
                      <span>45/100</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cache Hit Rate:</span>
                      <span>94%</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium">System Load</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Load Average:</span>
                      <span>1.2, 1.5, 1.8</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Processes:</span>
                      <span>156</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Uptime:</span>
                      <span>15d 4h 23m</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Services Tab */}
        <TabsContent value="services">
          <Card>
            <CardHeader>
              <CardTitle>Service Management</CardTitle>
              <CardDescription>Monitor and control system services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {services.map((service) => (
                  <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <Server className="h-5 w-5 text-gray-500" />
                      <div>
                        <h4 className="font-medium">{service.name}</h4>
                        <p className="text-sm text-gray-500">
                          Port {service.port} • Uptime: {service.uptime}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant={service.status === "running" ? "default" : "secondary"}>{service.status}</Badge>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Restart
                        </Button>
                        <Button variant="outline" size="sm">
                          Stop
                        </Button>
                        <Button variant="outline" size="sm">
                          Logs
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>Recent system alerts and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div key={alert.id} className="flex items-start gap-4 p-4 border rounded-lg">
                    {alert.type === "warning" ? (
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                    ) : (
                      <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <p className="font-medium">{alert.message}</p>
                      <p className="text-sm text-gray-500 mt-1">{alert.time}</p>
                    </div>
                    <Button variant="outline" size="sm">
                      Dismiss
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Logs Tab */}
        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle>System Logs</CardTitle>
              <CardDescription>Recent system log entries</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-96 overflow-y-auto">
                <div>[2024-01-15 14:30:01] INFO: System started successfully</div>
                <div>[2024-01-15 14:30:02] INFO: Database connection established</div>
                <div>[2024-01-15 14:30:03] INFO: Redis cache connected</div>
                <div>[2024-01-15 14:30:05] INFO: API server listening on port 3000</div>
                <div>[2024-01-15 14:35:12] WARN: High memory usage detected: 85%</div>
                <div>[2024-01-15 14:40:21] INFO: Backup process started</div>
                <div>[2024-01-15 14:45:33] INFO: Backup completed successfully</div>
                <div>[2024-01-15 15:12:45] ERROR: Failed to connect to external API</div>
                <div>[2024-01-15 15:12:46] INFO: Retrying external API connection</div>
                <div>[2024-01-15 15:12:48] INFO: External API connection restored</div>
              </div>
              <div className="flex justify-between items-center mt-4">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    Download Logs
                  </Button>
                  <Button variant="outline" size="sm">
                    Clear Logs
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Label htmlFor="log-level">Log Level:</Label>
                  <select className="px-2 py-1 border rounded">
                    <option>All</option>
                    <option>INFO</option>
                    <option>WARN</option>
                    <option>ERROR</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
