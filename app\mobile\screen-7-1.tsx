import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { X, SlidersHorizontal } from "lucide-react"

export default function MobileScreen7_1() {
  const priceRanges = ["Under ¥50", "¥50-¥100", "¥100-¥200", "¥200-¥500", "¥500+"]

  const categories = [
    "Electronics",
    "Fashion",
    "Home & Garden",
    "Books & Media",
    "Sports & Outdoors",
    "Beauty & Personal Care",
    "Toys & Games",
    "Art & Crafts",
  ]

  const occasions = ["Birthday", "Anniversary", "Christmas", "Valentine's Day", "Graduation", "Thank You"]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Bottom Sheet */}
      <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl z-20 max-h-[80vh] overflow-hidden">
        {/* Handle */}
        <div className="flex justify-center py-3">
          <div className="w-10 h-1 bg-gray-300 rounded-full"></div>
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-6 pb-4 border-b border-gray-100">
          <div className="flex items-center gap-2">
            <SlidersHorizontal className="w-5 h-5 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
          </div>
          <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Filter Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4 space-y-6">
          {/* Price Range */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Price Range</h3>
            <div className="space-y-3">
              {priceRanges.map((range, index) => (
                <div key={index} className="flex items-center gap-3">
                  <Checkbox id={`price-${index}`} />
                  <label htmlFor={`price-${index}`} className="text-sm text-gray-700">
                    {range}
                  </label>
                </div>
              ))}
            </div>

            {/* Custom Range */}
            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">Custom range</p>
              <div className="flex items-center gap-3">
                <Input type="number" placeholder="Min ¥" className="h-10" />
                <span className="text-gray-400">-</span>
                <Input type="number" placeholder="Max ¥" className="h-10" />
              </div>
            </div>
          </div>

          {/* Categories */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Categories</h3>
            <div className="space-y-3">
              {categories.map((category, index) => (
                <div key={index} className="flex items-center gap-3">
                  <Checkbox id={`category-${index}`} />
                  <label htmlFor={`category-${index}`} className="text-sm text-gray-700">
                    {category}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Occasions */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Occasions</h3>
            <div className="space-y-3">
              {occasions.map((occasion, index) => (
                <div key={index} className="flex items-center gap-3">
                  <Checkbox id={`occasion-${index}`} />
                  <label htmlFor={`occasion-${index}`} className="text-sm text-gray-700">
                    {occasion}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Rating */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Minimum Rating</h3>
            <div className="space-y-3">
              {[4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center gap-3">
                  <Checkbox id={`rating-${rating}`} />
                  <label htmlFor={`rating-${rating}`} className="text-sm text-gray-700 flex items-center gap-1">
                    {rating}+ stars
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className={`text-xs ${i < rating ? "text-yellow-400" : "text-gray-300"}`}>
                          ★
                        </span>
                      ))}
                    </div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Actions */}
        <div className="px-6 py-4 border-t border-gray-100 space-y-3">
          <div className="flex gap-3">
            <Button variant="outline" className="flex-1">
              Clear All
            </Button>
            <Button className="flex-1 bg-orange-500 hover:bg-orange-600">Apply Filters</Button>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
