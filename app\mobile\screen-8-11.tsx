import { Button } from "@/components/ui/button"
import { ArrowLeft, Bell, Mail, Smartphone, Globe, Shield, ChevronRight } from "lucide-react"

export default function MobileScreen8_11() {
  const settingsGroups = [
    {
      title: "Notifications",
      items: [
        {
          icon: Bell,
          label: "Push Notifications",
          description: "Get notified about gift recommendations",
          hasToggle: true,
          enabled: true,
        },
        {
          icon: Mail,
          label: "Email Notifications",
          description: "Receive updates via email",
          hasToggle: true,
          enabled: false,
        },
        {
          icon: Smartphone,
          label: "SMS Notifications",
          description: "Text message alerts for important dates",
          hasToggle: true,
          enabled: true,
        },
      ],
    },
    {
      title: "Privacy & Security",
      items: [
        {
          icon: Shield,
          label: "Privacy Settings",
          description: "Manage your data and privacy preferences",
          hasChevron: true,
        },
        {
          icon: Globe,
          label: "Data Usage",
          description: "Control how your data is used",
          hasChevron: true,
        },
      ],
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Settings</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-6 pb-24 overflow-y-auto">
        {settingsGroups.map((group, groupIndex) => (
          <div key={groupIndex} className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">{group.title}</h2>
            <div className="space-y-1">
              {group.items.map((item, itemIndex) => {
                const IconComponent = item.icon
                return (
                  <div
                    key={itemIndex}
                    className="flex items-center gap-4 p-4 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="w-5 h-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{item.label}</p>
                      <p className="text-sm text-gray-600">{item.description}</p>
                    </div>
                    {item.hasToggle && (
                      <div
                        className={`w-12 h-6 rounded-full relative transition-colors ${
                          item.enabled ? "bg-orange-500" : "bg-gray-200"
                        }`}
                      >
                        <div
                          className={`w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform shadow-sm ${
                            item.enabled ? "right-0.5" : "left-0.5"
                          }`}
                        ></div>
                      </div>
                    )}
                    {item.hasChevron && <ChevronRight className="w-5 h-5 text-gray-400" />}
                  </div>
                )
              })}
            </div>
          </div>
        ))}

        {/* App Information */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">App Information</h2>
          <div className="space-y-1">
            <div className="flex items-center justify-between p-4 rounded-xl">
              <span className="font-medium text-gray-900">Version</span>
              <span className="text-gray-600">1.2.0</span>
            </div>
            <div className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50">
              <span className="font-medium text-gray-900">Terms of Service</span>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-4 rounded-xl hover:bg-gray-50">
              <span className="font-medium text-gray-900">Privacy Policy</span>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Account</h2>
          <div className="space-y-3">
            <Button variant="outline" className="w-full text-red-600 border-red-200 hover:bg-red-50">
              Delete Account
            </Button>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
