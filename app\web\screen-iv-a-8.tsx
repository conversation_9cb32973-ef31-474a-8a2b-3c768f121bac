"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Save, Mail, Phone, MapPin, Calendar, Gift, Heart, ShoppingBag } from "lucide-react"

export default function UserProfileDetail() {
  const [isEditing, setIsEditing] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
            <div>
              <h1 className="text-2xl font-bold">User Profile</h1>
              <p className="text-gray-600">Detailed user information and activity</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => setIsEditing(!isEditing)}>
              {isEditing ? "Cancel" : "Edit Profile"}
            </Button>
            {isEditing && (
              <Button>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Overview */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6">
                <div className="text-center space-y-4">
                  <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full mx-auto flex items-center justify-center text-white text-2xl font-bold">
                    SJ
                  </div>
                  <div>
                    <h2 className="text-xl font-bold">Sarah Johnson</h2>
                    <p className="text-gray-600">Premium Member</p>
                    <Badge className="mt-2">Active</Badge>
                  </div>

                  <div className="space-y-3 text-left">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span className="text-sm"><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">+****************</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">New York, NY</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">Joined March 15, 2024</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Gift className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">Gifts Purchased</span>
                  </div>
                  <span className="font-semibold">47</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-red-500" />
                    <span className="text-sm">Wishlist Items</span>
                  </div>
                  <span className="font-semibold">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <ShoppingBag className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Total Spent</span>
                  </div>
                  <span className="font-semibold">$2,847</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="profile" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
                <TabsTrigger value="orders">Orders</TabsTrigger>
                <TabsTrigger value="support">Support</TabsTrigger>
              </TabsList>

              <TabsContent value="profile">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="firstName">First Name</Label>
                        <Input id="firstName" defaultValue="Sarah" disabled={!isEditing} />
                      </div>
                      <div>
                        <Label htmlFor="lastName">Last Name</Label>
                        <Input id="lastName" defaultValue="Johnson" disabled={!isEditing} />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input id="email" type="email" defaultValue="<EMAIL>" disabled={!isEditing} />
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input id="phone" defaultValue="+****************" disabled={!isEditing} />
                    </div>

                    <div>
                      <Label htmlFor="address">Address</Label>
                      <Textarea
                        id="address"
                        defaultValue="123 Main Street, Apt 4B, New York, NY 10001"
                        disabled={!isEditing}
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="birthday">Birthday</Label>
                        <Input id="birthday" type="date" defaultValue="1990-05-15" disabled={!isEditing} />
                      </div>
                      <div>
                        <Label htmlFor="accountType">Account Type</Label>
                        <Select defaultValue="premium" disabled={!isEditing}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="basic">Basic</SelectItem>
                            <SelectItem value="premium">Premium</SelectItem>
                            <SelectItem value="enterprise">Enterprise</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activity">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Activity</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          action: "Purchased gift",
                          item: "Wireless Headphones",
                          time: "2 hours ago",
                          amount: "$199.99",
                        },
                        { action: "Added to wishlist", item: "Smart Watch", time: "1 day ago", amount: null },
                        { action: "Purchased gift", item: "Coffee Maker", time: "3 days ago", amount: "$89.99" },
                        { action: "Created reminder", item: "Mom's Birthday", time: "1 week ago", amount: null },
                        { action: "Purchased gift", item: "Book Set", time: "2 weeks ago", amount: "$45.99" },
                      ].map((activity, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="font-medium">{activity.action}</p>
                            <p className="text-sm text-gray-600">{activity.item}</p>
                          </div>
                          <div className="text-right">
                            {activity.amount && <p className="font-semibold text-green-600">{activity.amount}</p>}
                            <p className="text-sm text-gray-500">{activity.time}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="orders">
                <Card>
                  <CardHeader>
                    <CardTitle>Order History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { id: "#ORD-001", date: "Dec 15, 2024", items: 2, total: "$199.99", status: "Delivered" },
                        { id: "#ORD-002", date: "Dec 12, 2024", items: 1, total: "$89.99", status: "Shipped" },
                        { id: "#ORD-003", date: "Dec 8, 2024", items: 3, total: "$145.97", status: "Delivered" },
                        { id: "#ORD-004", date: "Dec 1, 2024", items: 1, total: "$45.99", status: "Delivered" },
                      ].map((order, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <p className="font-medium">{order.id}</p>
                            <p className="text-sm text-gray-600">
                              {order.date} • {order.items} items
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">{order.total}</p>
                            <Badge variant={order.status === "Delivered" ? "default" : "secondary"}>
                              {order.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="support">
                <Card>
                  <CardHeader>
                    <CardTitle>Support Tickets</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        {
                          id: "#TICKET-123",
                          subject: "Unable to add items to wishlist",
                          status: "Open",
                          date: "Dec 15, 2024",
                        },
                        {
                          id: "#TICKET-122",
                          subject: "Payment processing issue",
                          status: "Resolved",
                          date: "Dec 10, 2024",
                        },
                        { id: "#TICKET-121", subject: "App crashes on startup", status: "Closed", date: "Dec 5, 2024" },
                      ].map((ticket, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <p className="font-medium">{ticket.id}</p>
                            <p className="text-sm text-gray-600">{ticket.subject}</p>
                          </div>
                          <div className="text-right">
                            <Badge
                              variant={
                                ticket.status === "Open"
                                  ? "destructive"
                                  : ticket.status === "Resolved"
                                    ? "default"
                                    : "secondary"
                              }
                            >
                              {ticket.status}
                            </Badge>
                            <p className="text-sm text-gray-500 mt-1">{ticket.date}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
