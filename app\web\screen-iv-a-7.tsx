"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Save, Send, AlertTriangle } from "lucide-react"

export default function UserSupportTicketDetail() {
  const [response, setResponse] = useState("")
  const [status, setStatus] = useState("open")
  const [priority, setPriority] = useState("medium")

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tickets
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Support Ticket #12345</h1>
              <p className="text-gray-600">Submitted by Sarah Johnson</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={status === "open" ? "destructive" : status === "in-progress" ? "default" : "secondary"}>
              {status.replace("-", " ").toUpperCase()}
            </Badge>
            <Badge variant={priority === "high" ? "destructive" : priority === "medium" ? "default" : "secondary"}>
              {priority.toUpperCase()} PRIORITY
            </Badge>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Original Ticket */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Original Issue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Subject</Label>
                    <p className="text-gray-900">Unable to add items to wishlist</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-gray-700 leading-relaxed">
                      Hi, I'm having trouble adding items to my wishlist. Every time I click the heart icon, it shows a
                      loading spinner but nothing happens. I've tried refreshing the page and logging out and back in,
                      but the issue persists. This started happening yesterday after the app update. Please help!
                    </p>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>Submitted: Dec 15, 2024 at 2:30 PM</span>
                    <span>Category: Technical Issue</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Conversation History */}
            <Card>
              <CardHeader>
                <CardTitle>Conversation History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Support Agent (Mike)</span>
                      <span className="text-sm text-gray-500">Dec 15, 2024 at 3:15 PM</span>
                    </div>
                    <p className="text-gray-700">
                      Hi Sarah, thank you for reaching out. I understand you're having trouble with the wishlist
                      feature. Can you please tell me which device and browser you're using?
                    </p>
                  </div>

                  <div className="border-l-4 border-green-500 pl-4">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">Sarah Johnson</span>
                      <span className="text-sm text-gray-500">Dec 15, 2024 at 4:22 PM</span>
                    </div>
                    <p className="text-gray-700">
                      I'm using an iPhone 14 with Safari browser. The app version is 2.1.3.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Response Form */}
            <Card>
              <CardHeader>
                <CardTitle>Send Response</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="response">Your Response</Label>
                    <Textarea
                      id="response"
                      placeholder="Type your response here..."
                      value={response}
                      onChange={(e) => setResponse(e.target.value)}
                      rows={6}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button>
                      <Send className="h-4 w-4 mr-2" />
                      Send Response
                    </Button>
                    <Button variant="outline">
                      <Save className="h-4 w-4 mr-2" />
                      Save Draft
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Ticket Details */}
            <Card>
              <CardHeader>
                <CardTitle>Ticket Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">Status</Label>
                  <Select value={status} onValueChange={setStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="open">Open</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium">Priority</Label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium">Assigned To</Label>
                  <Select defaultValue="mike">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mike">Mike Johnson</SelectItem>
                      <SelectItem value="sarah">Sarah Wilson</SelectItem>
                      <SelectItem value="david">David Chen</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button className="w-full">Update Ticket</Button>
              </CardContent>
            </Card>

            {/* User Information */}
            <Card>
              <CardHeader>
                <CardTitle>User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Name</Label>
                  <p className="text-gray-900">Sarah Johnson</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Email</Label>
                  <p className="text-gray-900"><EMAIL></p>
                </div>
                <div>
                  <Label className="text-sm font-medium">User ID</Label>
                  <p className="text-gray-900">#USER789</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">Account Type</Label>
                  <Badge variant="secondary">Premium</Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium">Join Date</Label>
                  <p className="text-gray-900">March 15, 2024</p>
                </div>
                <Button variant="outline" className="w-full">
                  View Full Profile
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
