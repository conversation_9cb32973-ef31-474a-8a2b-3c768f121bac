import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Heart, Share, Star, ExternalLink, ShoppingCart, Info, Truck, Shield } from "lucide-react"

export default function MobileScreen4_1() {
  const relatedGifts = [
    {
      id: 1,
      name: "Premium Wireless Earbuds",
      price: "¥199-299",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.3,
    },
    {
      id: 2,
      name: "Bluetooth Speaker Set",
      price: "¥150-250",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.5,
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Gift Details</h1>
        <div className="flex items-center gap-3">
          <Heart className="w-6 h-6 text-gray-600" />
          <Share className="w-6 h-6 text-gray-600" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 pb-32 overflow-y-auto">
        {/* Product Image */}
        <div className="px-4 py-4">
          <div className="bg-gray-100 rounded-xl aspect-square flex items-center justify-center">
            <img
              src="/placeholder.svg?height=300&width=300"
              alt="Wireless Bluetooth Headphones"
              className="w-full h-full object-cover rounded-xl"
            />
          </div>
        </div>

        {/* Product Info */}
        <div className="px-4 py-4">
          <h2 className="text-xl font-bold text-gray-900 mb-2">Wireless Bluetooth Headphones</h2>
          <div className="flex items-center gap-2 mb-3">
            <div className="flex items-center gap-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span className="text-sm font-medium">4.5</span>
            </div>
            <span className="text-sm text-gray-500">•</span>
            <span className="text-sm text-gray-600">TechSound</span>
            <span className="text-sm text-gray-500">•</span>
            <span className="text-sm text-gray-600">1,247 reviews</span>
          </div>
          <div className="text-2xl font-bold text-gray-900 mb-4">¥299-399</div>

          {/* AI Recommendation Reason */}
          <div className="bg-blue-50 rounded-xl p-4 border border-blue-100 mb-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Info className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-blue-900 mb-1">Why this gift?</h3>
                <p className="text-sm text-blue-800">
                  Perfect for your friend who loves music and tech. Based on their interests in audio equipment and
                  active lifestyle, these headphones offer excellent sound quality and comfort.
                </p>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-3">Key Features</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Active Noise Cancellation</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">30-hour battery life</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Premium comfort design</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Quick charge technology</span>
              </div>
            </div>
          </div>

          {/* Shipping & Returns */}
          <div className="mb-6">
            <h3 className="font-semibold text-gray-900 mb-3">Shipping & Returns</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Truck className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">Free shipping</p>
                  <p className="text-xs text-gray-600">Arrives in 2-3 business days</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-gray-900">30-day returns</p>
                  <p className="text-xs text-gray-600">Free returns & exchanges</p>
                </div>
              </div>
            </div>
          </div>

          {/* Related Gifts */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">You might also like</h3>
            <div className="space-y-3">
              {relatedGifts.map((gift) => (
                <div key={gift.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                  <img
                    src={gift.image || "/placeholder.svg"}
                    alt={gift.name}
                    className="w-16 h-16 rounded-lg object-cover bg-gray-100"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900 text-sm">{gift.name}</h4>
                    <div className="flex items-center gap-1 mt-1">
                      <Star className="w-3 h-3 text-yellow-400 fill-current" />
                      <span className="text-xs text-gray-600">{gift.rating}</span>
                    </div>
                    <p className="text-sm font-semibold text-gray-900 mt-1">{gift.price}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">
          <ExternalLink className="w-5 h-5 mr-2" />
          Buy from TechSound Store
        </Button>
        <Button variant="outline" className="w-full h-12">
          <ShoppingCart className="w-5 h-5 mr-2" />
          Add to Wishlist
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
