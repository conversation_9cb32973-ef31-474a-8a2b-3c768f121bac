"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Shield,
  Eye,
  FileText,
  Search,
  Download,
  Filter,
  Calendar,
  User,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react"

export default function AuditLogsScreen() {
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")

  const auditLogs = [
    {
      id: 1,
      timestamp: "2024-01-15 14:30:15",
      user: "<EMAIL>",
      action: "User Login",
      resource: "Authentication System",
      details: "Successful login from IP *************",
      level: "info",
      ip: "*************",
    },
    {
      id: 2,
      timestamp: "2024-01-15 14:25:22",
      user: "<EMAIL>",
      action: "User Created",
      resource: "User Management",
      details: "New user account created: <EMAIL>",
      level: "info",
      ip: "*************",
    },
    {
      id: 3,
      timestamp: "2024-01-15 14:20:08",
      user: "system",
      action: "Backup Completed",
      resource: "System Backup",
      details: "Automated daily backup completed successfully (2.4 GB)",
      level: "info",
      ip: "localhost",
    },
    {
      id: 4,
      timestamp: "2024-01-15 14:15:45",
      user: "<EMAIL>",
      action: "Configuration Changed",
      resource: "System Settings",
      details: "Modified email notification settings",
      level: "warning",
      ip: "*************",
    },
    {
      id: 5,
      timestamp: "2024-01-15 14:10:33",
      user: "unknown",
      action: "Failed Login Attempt",
      resource: "Authentication System",
      details: "Failed login attempt for user: <EMAIL>",
      level: "error",
      ip: "************",
    },
    {
      id: 6,
      timestamp: "2024-01-15 14:05:12",
      user: "<EMAIL>",
      action: "Data Export",
      resource: "User Data",
      details: "Exported user analytics report (Q4 2023)",
      level: "info",
      ip: "*************",
    },
    {
      id: 7,
      timestamp: "2024-01-15 14:00:01",
      user: "system",
      action: "Database Maintenance",
      resource: "Database",
      details: "Automated database optimization completed",
      level: "info",
      ip: "localhost",
    },
    {
      id: 8,
      timestamp: "2024-01-15 13:55:47",
      user: "<EMAIL>",
      action: "Permission Changed",
      resource: "User Management",
      details: "Updated permissions for user: <EMAIL>",
      level: "warning",
      ip: "*************",
    },
  ]

  const securityEvents = [
    {
      id: 1,
      type: "suspicious_login",
      severity: "high",
      description: "Multiple failed login attempts from IP ************",
      timestamp: "2024-01-15 14:10:33",
      status: "active",
    },
    {
      id: 2,
      type: "unusual_access",
      severity: "medium",
      description: "Admin access from new location (Singapore)",
      timestamp: "2024-01-15 12:30:15",
      status: "resolved",
    },
    {
      id: 3,
      type: "data_export",
      severity: "low",
      description: "Large data <NAME_EMAIL>",
      timestamp: "2024-01-15 14:05:12",
      status: "reviewed",
    },
  ]

  const complianceReports = [
    {
      id: 1,
      name: "GDPR Compliance Report",
      period: "Q4 2023",
      status: "completed",
      date: "2024-01-01",
      violations: 0,
    },
    {
      id: 2,
      name: "Security Audit Report",
      period: "December 2023",
      status: "completed",
      date: "2023-12-31",
      violations: 2,
    },
    {
      id: 3,
      name: "Data Access Report",
      period: "November 2023",
      status: "completed",
      date: "2023-11-30",
      violations: 0,
    },
  ]

  const filteredLogs = auditLogs.filter((log) => {
    if (selectedFilter !== "all" && log.level !== selectedFilter) return false
    if (
      searchQuery &&
      !log.action.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !log.user.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !log.details.toLowerCase().includes(searchQuery.toLowerCase())
    )
      return false
    return true
  })

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Audit Logs & Security</h1>
          <p className="text-gray-600">Monitor system activities and security events</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Switch checked={autoRefresh} onCheckedChange={setAutoRefresh} />
            <Label>Auto Refresh</Label>
          </div>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </Button>
        </div>
      </div>

      <Tabs defaultValue="logs" className="space-y-6">
        <TabsList>
          <TabsTrigger value="logs">Audit Logs</TabsTrigger>
          <TabsTrigger value="security">Security Events</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="settings">Log Settings</TabsTrigger>
        </TabsList>

        {/* Audit Logs Tab */}
        <TabsContent value="logs">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                System Audit Logs
              </CardTitle>
              <CardDescription>Comprehensive log of all system activities and user actions</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="mb-6 flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search logs..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <select
                    value={selectedFilter}
                    onChange={(e) => setSelectedFilter(e.target.value)}
                    className="px-3 py-2 border rounded-md"
                  >
                    <option value="all">All Levels</option>
                    <option value="info">Info</option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                </div>
                <Button variant="outline" size="sm">
                  <Calendar className="h-4 w-4 mr-2" />
                  Date Range
                </Button>
              </div>

              {/* Logs Table */}
              <div className="space-y-2">
                {filteredLogs.map((log) => (
                  <div key={log.id} className="flex items-start gap-4 p-4 border rounded-lg hover:bg-gray-50">
                    <div
                      className={`w-3 h-3 rounded-full mt-2 ${
                        log.level === "info" ? "bg-blue-500" : log.level === "warning" ? "bg-yellow-500" : "bg-red-500"
                      }`}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-gray-900">{log.action}</h4>
                        <Badge
                          variant={
                            log.level === "info" ? "default" : log.level === "warning" ? "secondary" : "destructive"
                          }
                        >
                          {log.level}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{log.details}</p>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {log.timestamp}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {log.user}
                        </span>
                        <span>{log.resource}</span>
                        <span>IP: {log.ip}</span>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div className="mt-6 flex items-center justify-between">
                <p className="text-sm text-gray-500">
                  Showing {filteredLogs.length} of {auditLogs.length} logs
                </p>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    Previous
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Events Tab */}
        <TabsContent value="security">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Threats</CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">3</div>
                <p className="text-xs text-muted-foreground">Require immediate attention</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Resolved Today</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">12</div>
                <p className="text-xs text-muted-foreground">Security events resolved</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
                <Shield className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">Medium</div>
                <p className="text-xs text-muted-foreground">Overall security risk level</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Events
              </CardTitle>
              <CardDescription>Recent security events and threats</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {securityEvents.map((event) => (
                  <div key={event.id} className="flex items-start gap-4 p-4 border rounded-lg">
                    <AlertTriangle
                      className={`h-5 w-5 mt-0.5 ${
                        event.severity === "high"
                          ? "text-red-500"
                          : event.severity === "medium"
                            ? "text-yellow-500"
                            : "text-blue-500"
                      }`}
                    />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{event.description}</h4>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              event.severity === "high"
                                ? "destructive"
                                : event.severity === "medium"
                                  ? "secondary"
                                  : "default"
                            }
                          >
                            {event.severity}
                          </Badge>
                          <Badge
                            variant={
                              event.status === "active"
                                ? "destructive"
                                : event.status === "resolved"
                                  ? "default"
                                  : "secondary"
                            }
                          >
                            {event.status}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500">{event.timestamp}</p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        Investigate
                      </Button>
                      <Button variant="outline" size="sm">
                        Resolve
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Compliance Tab */}
        <TabsContent value="compliance">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Compliance Reports</CardTitle>
                <CardDescription>Generated compliance and audit reports</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {complianceReports.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-medium">{report.name}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Period: {report.period}</span>
                          <span>Generated: {report.date}</span>
                          <span
                            className={`flex items-center gap-1 ${
                              report.violations === 0 ? "text-green-600" : "text-red-600"
                            }`}
                          >
                            {report.violations === 0 ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <AlertTriangle className="h-3 w-3" />
                            )}
                            {report.violations} violations
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={report.status === "completed" ? "default" : "secondary"}>{report.status}</Badge>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>

                <Button className="w-full mt-4" variant="outline">
                  Generate New Report
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Compliance Status</CardTitle>
                <CardDescription>Current compliance with various regulations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">GDPR Compliance</span>
                      <Badge variant="default">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">ISO 27001</span>
                      <Badge variant="default">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">SOC 2 Type II</span>
                      <Badge variant="secondary">In Progress</Badge>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Data Retention</span>
                      <Badge variant="default">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Access Controls</span>
                      <Badge variant="default">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Audit Trail</span>
                      <Badge variant="default">Compliant</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Log Settings Tab */}
        <TabsContent value="settings">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Log Configuration</CardTitle>
                <CardDescription>Configure audit log settings and retention</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="log-level">Log Level</Label>
                  <select id="log-level" className="w-full px-3 py-2 border rounded-md">
                    <option value="debug">Debug</option>
                    <option value="info" selected>
                      Info
                    </option>
                    <option value="warning">Warning</option>
                    <option value="error">Error</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="retention-days">Log Retention (days)</Label>
                  <Input id="retention-days" type="number" defaultValue="90" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-log-size">Max Log File Size (MB)</Label>
                  <Input id="max-log-size" type="number" defaultValue="100" />
                </div>

                <div className="space-y-4">
                  <Label>Event Types to Log</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span>User Authentication</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span>Data Access</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span>Configuration Changes</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span>System Events</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" />
                      <span>API Requests</span>
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security Monitoring</CardTitle>
                <CardDescription>Configure security event detection and alerts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="failed-login-threshold">Failed Login Threshold</Label>
                  <Input id="failed-login-threshold" type="number" defaultValue="5" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="suspicious-ip-timeout">Suspicious IP Timeout (minutes)</Label>
                  <Input id="suspicious-ip-timeout" type="number" defaultValue="30" />
                </div>

                <div className="space-y-4">
                  <Label>Alert Settings</Label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span>Email Alerts</span>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>SMS Alerts</span>
                      <Switch />
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Real-time Notifications</span>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="alert-email">Alert Email Recipients</Label>
                  <Textarea id="alert-email" placeholder="<EMAIL>, <EMAIL>" rows={3} />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
