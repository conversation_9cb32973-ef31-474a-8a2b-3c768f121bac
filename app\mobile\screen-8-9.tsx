"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Camera, User } from "lucide-react"
import { useState } from "react"

export default function MobileScreen8_9() {
  const [firstName, setFirstName] = useState("Jeson")
  const [lastName, setLastName] = useState("Chen")
  const [email, setEmail] = useState("<EMAIL>")
  const [phone, setPhone] = useState("+86 138 0013 8000")
  const [birthday, setBirthday] = useState("1990-05-15")
  const [gender, setGender] = useState("male")

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Edit Profile</h1>
        <span className="text-orange-500 font-medium">Save</span>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-6 pb-24 overflow-y-auto">
        {/* Profile Photo */}
        <div className="flex flex-col items-center mb-8">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-orange-400 to-pink-500 rounded-full flex items-center justify-center">
              <User className="w-12 h-12 text-white" />
            </div>
            <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center shadow-lg">
              <Camera className="w-4 h-4 text-white" />
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-3">Tap to change photo</p>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">First Name</label>
              <Input value={firstName} onChange={(e) => setFirstName(e.target.value)} className="h-12" />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Last Name</label>
              <Input value={lastName} onChange={(e) => setLastName(e.target.value)} className="h-12" />
            </div>
          </div>

          {/* Email */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Email Address</label>
            <Input type="email" value={email} onChange={(e) => setEmail(e.target.value)} className="h-12" />
          </div>

          {/* Phone */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Phone Number</label>
            <Input type="tel" value={phone} onChange={(e) => setPhone(e.target.value)} className="h-12" />
          </div>

          {/* Birthday */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Birthday</label>
            <Input type="date" value={birthday} onChange={(e) => setBirthday(e.target.value)} className="h-12" />
          </div>

          {/* Gender */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Gender</label>
            <Select value={gender} onValueChange={setGender}>
              <SelectTrigger className="h-12">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="other">Other</SelectItem>
                <SelectItem value="prefer-not-say">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Preferences Section */}
          <div className="pt-4 border-t border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Email Notifications</p>
                  <p className="text-sm text-gray-600">Receive gift recommendations and updates</p>
                </div>
                <div className="w-12 h-6 bg-orange-500 rounded-full relative">
                  <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5 shadow-sm"></div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">Push Notifications</p>
                  <p className="text-sm text-gray-600">Get notified about important dates</p>
                </div>
                <div className="w-12 h-6 bg-gray-200 rounded-full relative">
                  <div className="w-5 h-5 bg-white rounded-full absolute left-0.5 top-0.5 shadow-sm"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-6 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">Save Changes</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Cancel
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
