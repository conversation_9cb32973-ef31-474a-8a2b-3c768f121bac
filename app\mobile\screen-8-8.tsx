import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Edit, Heart, Calendar, Settings, HelpCircle, Star, ChevronRight, LogOut } from "lucide-react"

export default function MobileScreen8_8() {
  const menuItems = [
    { icon: Heart, label: "My Wishlist / Saved Gifts", hasChevron: true },
    { icon: Calendar, label: "Important Date Reminders", hasChevron: true },
    { icon: Settings, label: "Settings", hasChevron: true },
    { icon: HelpCircle, label: "Help & Support", hasChevron: true },
    { icon: Star, label: "Rate Us on the App Store", hasChevron: true },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">My Profile</h1>
        <Edit className="w-6 h-6 text-gray-600" />
      </div>

      {/* User Info Section */}
      <div className="px-6 py-6 border-b border-gray-100">
        <div className="flex items-center gap-4">
          {/* Avatar */}
          <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-pink-500 rounded-full flex items-center justify-center">
            <span className="text-white font-semibold text-lg">JC</span>
          </div>

          {/* User Details */}
          <div className="flex-1">
            <h2 className="text-xl font-semibold text-gray-900">Jeson Chen</h2>
            <p className="text-gray-600"><EMAIL></p>
          </div>
        </div>

        {/* Edit Profile Button */}
        <Button variant="outline" className="w-full mt-4">
          Edit Profile
        </Button>
      </div>

      {/* Menu Items */}
      <div className="flex-1 px-4 py-4">
        <div className="space-y-1">
          {menuItems.map((item, index) => {
            const IconComponent = item.icon
            return (
              <div key={index} className="flex items-center gap-4 p-4 rounded-xl hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                  <IconComponent className="w-5 h-5 text-gray-600" />
                </div>
                <span className="flex-1 font-medium text-gray-900">{item.label}</span>
                {item.hasChevron && <ChevronRight className="w-5 h-5 text-gray-400" />}
              </div>
            )
          })}
        </div>
      </div>

      {/* Logout Button */}
      <div className="px-4 pb-8">
        <Button variant="outline" className="w-full text-red-600 border-red-200 hover:bg-red-50">
          <LogOut className="w-5 h-5 mr-2" />
          Log Out
        </Button>
      </div>

      {/* Bottom Navigation */}
      <div className="absolute bottom-8 left-0 right-0 bg-white/90 backdrop-blur-sm mx-4 rounded-2xl border border-white/20">
        <div className="flex items-center justify-around py-3">
          <div className="flex flex-col items-center gap-1">
            <div className="w-6 h-6 text-gray-400"></div>
            <span className="text-xs text-gray-400">Recommend</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <div className="w-6 h-6 text-gray-400"></div>
            <span className="text-xs text-gray-400">Discover</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <div className="w-6 h-6 text-gray-400"></div>
            <span className="text-xs text-gray-400">My Dates</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white text-xs">👤</span>
            </div>
            <span className="text-xs font-medium text-orange-500">Profile</span>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
