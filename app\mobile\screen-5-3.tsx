import { Button } from "@/components/ui/button"
import { AlertTriangle, X } from "lucide-react"

export default function MobileScreen5_3() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Error Modal */}
      <div className="absolute inset-0 flex items-center justify-center z-20 p-6">
        <div className="bg-white rounded-2xl p-6 w-full max-w-sm shadow-2xl">
          {/* Close Button */}
          <div className="flex justify-end mb-4">
            <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              <X className="w-5 h-5 text-gray-600" />
            </button>
          </div>

          {/* Error Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-center text-gray-900 mb-4">Connection Error</h2>

          {/* Description */}
          <p className="text-gray-600 text-center mb-6 leading-relaxed">
            We couldn't connect to the partner store right now. Please check your internet connection and try again.
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">Try Again</Button>
            <Button variant="outline" className="w-full">
              View Similar Products
            </Button>
            <Button variant="ghost" className="w-full text-gray-500">
              Cancel
            </Button>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
