"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Calendar, X } from "lucide-react"
import { useState } from "react"

export default function MobileScreen9_2() {
  const [dateType, setDateType] = useState("")
  const [dateName, setDateName] = useState("")
  const [dateValue, setDateValue] = useState("")
  const [personName, setPersonName] = useState("")
  const [relationship, setRelationship] = useState("")
  const [repeat, setRepeat] = useState("yearly")
  const [reminder, setReminder] = useState("1-week")

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Add New Date</h1>
        <X className="w-6 h-6 text-gray-600" />
      </div>

      {/* Form Content */}
      <div className="flex-1 px-6 py-6 pb-32 overflow-y-auto space-y-6">
        {/* Date Type */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Date Type</label>
          <Select value={dateType} onValueChange={setDateType}>
            <SelectTrigger className="h-12">
              <SelectValue placeholder="Select date type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="birthday">Birthday</SelectItem>
              <SelectItem value="anniversary">Anniversary</SelectItem>
              <SelectItem value="holiday">Holiday</SelectItem>
              <SelectItem value="graduation">Graduation</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Date Name */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Date Name</label>
          <Input
            placeholder="e.g., Mom's Birthday, Wedding Anniversary"
            value={dateName}
            onChange={(e) => setDateName(e.target.value)}
            className="h-12"
          />
        </div>

        {/* Date */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Date</label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type="date"
              value={dateValue}
              onChange={(e) => setDateValue(e.target.value)}
              className="pl-12 h-12"
            />
          </div>
        </div>

        {/* Person Name */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Person Name</label>
          <Input
            placeholder="Who is this date for?"
            value={personName}
            onChange={(e) => setPersonName(e.target.value)}
            className="h-12"
          />
        </div>

        {/* Relationship */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Relationship</label>
          <Select value={relationship} onValueChange={setRelationship}>
            <SelectTrigger className="h-12">
              <SelectValue placeholder="Select relationship" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="family">Family</SelectItem>
              <SelectItem value="friend">Friend</SelectItem>
              <SelectItem value="partner">Partner/Spouse</SelectItem>
              <SelectItem value="colleague">Colleague</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Repeat */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Repeat</label>
          <Select value={repeat} onValueChange={setRepeat}>
            <SelectTrigger className="h-12">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="yearly">Yearly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="once">One-time only</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Reminder */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Reminder</label>
          <Select value={reminder} onValueChange={setReminder}>
            <SelectTrigger className="h-12">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1-month">1 month before</SelectItem>
              <SelectItem value="2-weeks">2 weeks before</SelectItem>
              <SelectItem value="1-week">1 week before</SelectItem>
              <SelectItem value="3-days">3 days before</SelectItem>
              <SelectItem value="1-day">1 day before</SelectItem>
              <SelectItem value="same-day">On the day</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Notes */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Notes (Optional)</label>
          <Input placeholder="Add any gift ideas or notes" className="h-12" />
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-6 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">Save Date</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Cancel
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
