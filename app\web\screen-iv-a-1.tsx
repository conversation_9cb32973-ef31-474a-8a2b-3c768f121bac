import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Sparkles } from "lucide-react"

export default function WebScreenIVA1() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-pink-500 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">GiftSpark AI - Partner Portal</span>
            </div>
            <nav className="flex items-center gap-6">
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Features
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Pricing
              </a>
              <a href="#" className="text-orange-600 hover:text-orange-700 font-medium">
                Login
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Left Column - Info */}
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-6">Become a GiftSpark AI Partner</h1>
            <p className="text-lg text-gray-600 mb-8">
              Join our network of brands and reach millions of gift-seekers through our AI-powered recommendation
              platform.
            </p>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Increased Visibility</h3>
                  <p className="text-gray-600">Get your products in front of customers actively looking for gifts</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">AI-Powered Matching</h3>
                  <p className="text-gray-600">Our AI ensures your products reach the right customers</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                  <span className="text-green-600 text-sm">✓</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Performance Analytics</h3>
                  <p className="text-gray-600">Track your success with detailed performance insights</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Registration Form */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Create Partner Account</h2>

            <form className="space-y-6">
              <div>
                <Label htmlFor="company">Company Name</Label>
                <Input id="company" placeholder="Enter your company name" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="contact">Contact Person Name</Label>
                <Input id="contact" placeholder="Enter contact person name" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="email">Business Email Address</Label>
                <Input id="email" type="email" placeholder="Enter business email" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="password">Create Password</Label>
                <Input id="password" type="password" placeholder="Create a secure password" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="confirm-password">Confirm Password</Label>
                <Input id="confirm-password" type="password" placeholder="Confirm your password" className="mt-1" />
              </div>

              <div>
                <Label htmlFor="category">Business Category/Industry</Label>
                <Select>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select your industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electronics">Electronics & Tech</SelectItem>
                    <SelectItem value="fashion">Fashion & Accessories</SelectItem>
                    <SelectItem value="home">Home & Garden</SelectItem>
                    <SelectItem value="books">Books & Media</SelectItem>
                    <SelectItem value="sports">Sports & Outdoors</SelectItem>
                    <SelectItem value="beauty">Beauty & Personal Care</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="website">Website URL (Optional)</Label>
                <Input id="website" placeholder="https://yourwebsite.com" className="mt-1" />
              </div>

              <div className="flex items-start gap-3">
                <Checkbox id="terms" className="mt-1" />
                <Label htmlFor="terms" className="text-sm leading-relaxed">
                  I agree to the{" "}
                  <a href="#" className="text-orange-600 hover:underline">
                    Partner Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="#" className="text-orange-600 hover:underline">
                    Privacy Policy
                  </a>
                </Label>
              </div>

              <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white">Create Account</Button>
            </form>

            <p className="text-center text-sm text-gray-600 mt-6">
              Already have a partner account?{" "}
              <a href="#" className="text-orange-600 hover:underline font-medium">
                Login here
              </a>
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex justify-between items-center">
            <p className="text-gray-600">© 2024 GiftSpark AI. All rights reserved.</p>
            <div className="flex gap-6">
              <a href="#" className="text-gray-600 hover:text-gray-900">
                About Us
              </a>
              <a href="#" className="text-gray-600 hover:text-gray-900">
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
