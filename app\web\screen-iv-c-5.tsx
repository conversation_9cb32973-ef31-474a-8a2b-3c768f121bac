"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { ArrowLeft, Save, Plus, Trash2 } from "lucide-react"
import Link from "next/link"

export default function WebScreenIVC5() {
  const [categories, setCategories] = useState([
    { id: 1, name: "Gift Guides", slug: "gift-guides", description: "Curated gift recommendations", active: true },
    { id: 2, name: "Trends", slug: "trends", description: "Latest gift trends and insights", active: true },
    { id: 3, name: "Tips & Advice", slug: "tips-advice", description: "Helpful gift-giving tips", active: true },
  ])

  const [newCategory, setNewCategory] = useState({
    name: "",
    slug: "",
    description: "",
    active: true,
  })

  const handleAddCategory = () => {
    if (newCategory.name && newCategory.slug) {
      setCategories([
        ...categories,
        {
          id: Date.now(),
          ...newCategory,
        },
      ])
      setNewCategory({ name: "", slug: "", description: "", active: true })
    }
  }

  const handleDeleteCategory = (id: number) => {
    setCategories(categories.filter((cat) => cat.id !== id))
  }

  const handleToggleActive = (id: number) => {
    setCategories(categories.map((cat) => (cat.id === id ? { ...cat, active: !cat.active } : cat)))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/web/screen-iv-c-1">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Content
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">Category Management</h1>
              <p className="text-gray-600">Manage content categories and tags</p>
            </div>
          </div>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* Add New Category */}
          <Card>
            <CardHeader>
              <CardTitle>Add New Category</CardTitle>
              <CardDescription>Create a new content category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="category-name">Category Name</Label>
                  <Input
                    id="category-name"
                    placeholder="Enter category name"
                    value={newCategory.name}
                    onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="category-slug">Slug</Label>
                  <Input
                    id="category-slug"
                    placeholder="category-slug"
                    value={newCategory.slug}
                    onChange={(e) => setNewCategory({ ...newCategory, slug: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="category-description">Description</Label>
                  <Input
                    id="category-description"
                    placeholder="Brief description"
                    value={newCategory.description}
                    onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={handleAddCategory} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Category
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Existing Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Existing Categories</CardTitle>
              <CardDescription>Manage your content categories</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="font-medium">{category.name}</h3>
                          <p className="text-sm text-gray-600">/{category.slug}</p>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-600">{category.description}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Label htmlFor={`active-${category.id}`} className="text-sm">
                          Active
                        </Label>
                        <Switch
                          id={`active-${category.id}`}
                          checked={category.active}
                          onCheckedChange={() => handleToggleActive(category.id)}
                        />
                      </div>
                      <Button variant="outline" size="sm" onClick={() => handleDeleteCategory(category.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tag Management */}
          <Card>
            <CardHeader>
              <CardTitle>Tag Management</CardTitle>
              <CardDescription>Manage content tags</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="new-tag">Add New Tag</Label>
                  <div className="flex gap-2 mt-2">
                    <Input id="new-tag" placeholder="Enter tag name" />
                    <Button>Add</Button>
                  </div>
                </div>
                <div>
                  <Label>Popular Tags</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {["holiday-gifts", "birthday", "anniversary", "wedding", "baby-shower", "graduation"].map((tag) => (
                      <div key={tag} className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded text-sm">
                        {tag}
                        <Button variant="ghost" size="sm" className="h-4 w-4 p-0">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
              <CardDescription>Configure SEO settings for categories</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="default-meta-title">Default Meta Title Template</Label>
                  <Input id="default-meta-title" placeholder="{category} - GiftSpark" />
                </div>
                <div>
                  <Label htmlFor="default-meta-description">Default Meta Description Template</Label>
                  <Textarea id="default-meta-description" placeholder="Discover amazing {category} on GiftSpark..." />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
