import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Mail, CheckCircle } from "lucide-react"

export default function MobileScreen8_6() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Reset Password</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8 flex flex-col items-center justify-center">
        {/* Success Icon */}
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-8">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Check Your Email</h1>

        {/* Description */}
        <p className="text-gray-600 text-center mb-2 leading-relaxed">We've sent a password reset link to:</p>
        <p className="text-orange-600 font-medium text-center mb-8"><EMAIL></p>

        {/* Instructions */}
        <div className="w-full bg-blue-50 rounded-xl p-4 border border-blue-100 mb-8">
          <div className="flex items-start gap-3">
            <Mail className="w-5 h-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Next Steps:</h4>
              <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                <li>Check your email inbox</li>
                <li>Click the reset link in the email</li>
                <li>Create your new password</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="w-full space-y-3">
          <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold">
            Open Email App
          </Button>
          <Button variant="outline" className="w-full h-12">
            Resend Email
          </Button>
        </div>

        {/* Help Text */}
        <p className="text-xs text-gray-500 text-center mt-6 leading-relaxed">
          Didn't receive the email? Check your spam folder or try a different email address.
        </p>

        {/* Back to Login */}
        <div className="text-center mt-8">
          <span className="text-sm text-gray-600">
            <span className="text-orange-500 font-medium underline">Back to Login</span>
          </span>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
