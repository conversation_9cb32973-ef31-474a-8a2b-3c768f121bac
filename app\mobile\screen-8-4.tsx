import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, CheckCircle, <PERSON>rk<PERSON> } from "lucide-react"

export default function MobileScreen8_4() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Login Successful</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8 flex flex-col items-center justify-center">
        {/* Success Icon */}
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-8">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Welcome Back!</h1>

        {/* Description */}
        <p className="text-gray-600 text-center mb-8 leading-relaxed">
          You've successfully logged in to your GiftSpark AI account. Ready to find the perfect gifts?
        </p>

        {/* Quick Stats */}
        <div className="w-full bg-gradient-to-r from-orange-50 to-pink-50 rounded-xl p-6 mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="w-5 h-5 text-orange-500" />
            <h3 className="font-semibold text-gray-900">Your GiftSpark Summary</h3>
          </div>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-xl font-bold text-gray-900">12</div>
              <div className="text-xs text-gray-600">Saved Gifts</div>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-900">5</div>
              <div className="text-xs text-gray-600">Upcoming Dates</div>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-900">8</div>
              <div className="text-xs text-gray-600">E-Cards Sent</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="w-full space-y-3">
          <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold">
            Continue to Home
          </Button>
          <Button variant="outline" className="w-full h-12">
            View My Profile
          </Button>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
