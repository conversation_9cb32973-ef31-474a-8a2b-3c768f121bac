import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, X, Heart, Star, ExternalLink } from "lucide-react"

export default function MobileScreen2_5() {
  const searchResults = [
    {
      id: 1,
      name: "Bluetooth Wireless Headphones",
      price: "¥299-399",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.5,
      brand: "AudioTech",
      matchReason: "Matches 'wireless headphones'",
    },
    {
      id: 2,
      name: "Premium Wireless Earbuds",
      price: "¥199-299",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.3,
      brand: "SoundPro",
      matchReason: "Similar to 'wireless headphones'",
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Search Header */}
      <div className="flex items-center gap-3 p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <div className="flex-1 relative">
          <Input type="text" value="wireless headphones" className="h-10 pr-10 bg-gray-50 border-0" readOnly />
          <button className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>

      {/* Results Header */}
      <div className="px-4 py-3 bg-gray-50 border-b border-gray-100">
        <h2 className="font-medium text-gray-900">Search Results for 'wireless headphones'</h2>
        <p className="text-sm text-gray-600 mt-1">Found 2 matching gifts</p>
      </div>

      {/* Search Results */}
      <div className="flex-1 px-4 py-4 space-y-4">
        {searchResults.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
            <div className="flex gap-4">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-20 h-20 rounded-lg object-cover bg-gray-100"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm leading-tight">{item.name}</h3>
                  <Heart className="w-5 h-5 text-gray-400 ml-2 flex-shrink-0" />
                </div>

                <p className="text-lg font-semibold text-gray-900 mb-1">{item.price}</p>

                <div className="flex items-center gap-1 mb-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{item.rating}</span>
                  <span className="text-sm text-gray-400">• {item.brand}</span>
                </div>

                <div className="bg-green-50 rounded-lg p-2 mb-3">
                  <p className="text-xs text-green-800">
                    <span className="font-medium">Match:</span> {item.matchReason}
                  </p>
                </div>

                <Button size="sm" className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          </div>
        ))}

        {/* No More Results */}
        <div className="text-center py-8">
          <p className="text-gray-500 text-sm">
            No more results found. Try different keywords or{" "}
            <span className="text-orange-500 underline">refine your criteria</span>
          </p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
