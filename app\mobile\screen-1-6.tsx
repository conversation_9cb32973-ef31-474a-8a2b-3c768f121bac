"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft } from "lucide-react"
import { useState } from "react"

export default function MobileScreen1_6() {
  const [selectedBudget, setSelectedBudget] = useState<string | null>(null)
  const [customMin, setCustomMin] = useState("")
  const [customMax, setCustomMax] = useState("")

  const budgetTiers = ["Under ¥50", "¥50-¥100", "¥100-¥200", "¥200-¥500", "¥500+", "Any Budget"]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">What's your budget range?</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Indicator */}
      <div className="px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 3 of 4</p>
      </div>

      {/* Budget Selection */}
      <div className="flex-1 px-4 pb-24">
        {/* Predefined Budget Tiers */}
        <div className="mb-8">
          <h3 className="text-sm font-medium text-gray-700 mb-4">Choose a budget range</h3>
          <div className="grid grid-cols-2 gap-3">
            {budgetTiers.map((budget, index) => (
              <Button
                key={index}
                variant={selectedBudget === budget ? "default" : "outline"}
                className={`h-12 ${
                  selectedBudget === budget
                    ? "bg-orange-500 hover:bg-orange-600 text-white border-orange-500"
                    : "border-gray-200 text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => setSelectedBudget(budget)}
              >
                {budget}
              </Button>
            ))}
          </div>
        </div>

        {/* Custom Budget Input */}
        <div className="mb-8">
          <h3 className="text-sm font-medium text-gray-700 mb-4">Or set a custom range</h3>
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <label className="text-xs text-gray-500 mb-1 block">Min ¥</label>
              <Input
                type="number"
                placeholder="0"
                value={customMin}
                onChange={(e) => setCustomMin(e.target.value)}
                className="h-12"
              />
            </div>
            <div className="text-gray-400 mt-6">-</div>
            <div className="flex-1">
              <label className="text-xs text-gray-500 mb-1 block">Max ¥</label>
              <Input
                type="number"
                placeholder="1000"
                value={customMax}
                onChange={(e) => setCustomMax(e.target.value)}
                className="h-12"
              />
            </div>
          </div>
        </div>

        {/* Budget Tips */}
        <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
          <h4 className="font-medium text-blue-900 mb-2">💡 Budget Tips</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Setting a range helps us find the best value gifts</li>
            <li>• You can always adjust this later</li>
            <li>• "Any Budget" shows all available options</li>
          </ul>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button
          className={`w-full h-12 ${
            selectedBudget || (customMin && customMax)
              ? "bg-orange-500 hover:bg-orange-600 text-white"
              : "bg-gray-200 text-gray-400"
          }`}
          disabled={!selectedBudget && !(customMin && customMax)}
        >
          Next
        </Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Back
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
