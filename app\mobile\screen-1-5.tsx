import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Users, Heart, User, Briefcase, Baby, GraduationCap, Check } from "lucide-react"

export default function MobileScreen1_5() {
  const relationships = [
    { name: "Friend", icon: Users, category: "Friends", selected: true },
    { name: "Partner/Spouse", icon: Heart, category: "Family", selected: false },
    { name: "Parent", icon: User, category: "Family", selected: false },
    { name: "Sibling", icon: Users, category: "Family", selected: false },
    { name: "Child", icon: Baby, category: "Family", selected: false },
    { name: "Colleague", icon: Briefcase, category: "Work", selected: false },
    { name: "Boss", icon: GraduationCap, category: "Work", selected: false },
    { name: "Client", icon: Briefcase, category: "Work", selected: false },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Who is this gift for?</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Indicator */}
      <div className="px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 2 of 4</p>
      </div>

      {/* Relationships List */}
      <div className="flex-1 px-4 pb-24">
        {/* Family Section */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-500 mb-3 px-2">FAMILY</h3>
          <div className="space-y-3">
            {relationships
              .filter((rel) => rel.category === "Family")
              .map((relationship, index) => {
                const IconComponent = relationship.icon
                return (
                  <div
                    key={index}
                    className={`flex items-center gap-4 p-4 rounded-xl border transition-colors ${
                      relationship.selected
                        ? "bg-orange-50 border-orange-200"
                        : "bg-gray-50 border-gray-100 hover:bg-gray-100"
                    }`}
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        relationship.selected ? "bg-orange-100" : "bg-white"
                      }`}
                    >
                      <IconComponent
                        className={`w-6 h-6 ${relationship.selected ? "text-orange-600" : "text-orange-500"}`}
                      />
                    </div>
                    <span
                      className={`flex-1 font-medium ${relationship.selected ? "text-orange-900" : "text-gray-900"}`}
                    >
                      {relationship.name}
                    </span>
                    {relationship.selected && (
                      <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                )
              })}
          </div>
        </div>

        {/* Friends Section */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-500 mb-3 px-2">FRIENDS</h3>
          <div className="space-y-3">
            {relationships
              .filter((rel) => rel.category === "Friends")
              .map((relationship, index) => {
                const IconComponent = relationship.icon
                return (
                  <div
                    key={index}
                    className={`flex items-center gap-4 p-4 rounded-xl border transition-colors ${
                      relationship.selected
                        ? "bg-orange-50 border-orange-200"
                        : "bg-gray-50 border-gray-100 hover:bg-gray-100"
                    }`}
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        relationship.selected ? "bg-orange-100" : "bg-white"
                      }`}
                    >
                      <IconComponent
                        className={`w-6 h-6 ${relationship.selected ? "text-orange-600" : "text-orange-500"}`}
                      />
                    </div>
                    <span
                      className={`flex-1 font-medium ${relationship.selected ? "text-orange-900" : "text-gray-900"}`}
                    >
                      {relationship.name}
                    </span>
                    {relationship.selected && (
                      <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                )
              })}
          </div>
        </div>

        {/* Work Section */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-500 mb-3 px-2">WORK</h3>
          <div className="space-y-3">
            {relationships
              .filter((rel) => rel.category === "Work")
              .map((relationship, index) => {
                const IconComponent = relationship.icon
                return (
                  <div
                    key={index}
                    className={`flex items-center gap-4 p-4 rounded-xl border transition-colors ${
                      relationship.selected
                        ? "bg-orange-50 border-orange-200"
                        : "bg-gray-50 border-gray-100 hover:bg-gray-100"
                    }`}
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        relationship.selected ? "bg-orange-100" : "bg-white"
                      }`}
                    >
                      <IconComponent
                        className={`w-6 h-6 ${relationship.selected ? "text-orange-600" : "text-orange-500"}`}
                      />
                    </div>
                    <span
                      className={`flex-1 font-medium ${relationship.selected ? "text-orange-900" : "text-gray-900"}`}
                    >
                      {relationship.name}
                    </span>
                    {relationship.selected && (
                      <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-white" />
                      </div>
                    )}
                  </div>
                )
              })}
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">Next</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Back
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
