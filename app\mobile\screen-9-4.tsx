import { <PERSON><PERSON> } from "@/components/ui/button"
import { Bell, Calendar, Gift, Sparkles } from "lucide-react"

export default function MobileScreen9_4() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Notification Modal */}
      <div className="absolute inset-0 flex items-center justify-center z-20 p-6">
        <div className="bg-white rounded-2xl p-6 w-full max-w-sm shadow-2xl">
          {/* Notification Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <Bell className="w-8 h-8 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-center text-gray-900 mb-2">Upcoming Date Reminder</h2>

          {/* Date Info */}
          <div className="bg-red-50 rounded-xl p-4 mb-4 flex items-center gap-3">
            <Calendar className="w-6 h-6 text-red-600" />
            <div>
              <p className="font-medium text-gray-900">Mom's Birthday</p>
              <p className="text-sm text-gray-600">June 15, 2025 • 12 days left</p>
            </div>
          </div>

          {/* Description */}
          <p className="text-gray-600 text-center mb-6">
            Don't forget to prepare a gift for this special occasion! Would you like to find gift recommendations now?
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
              <Sparkles className="w-5 h-5 mr-2" />
              Find Gift Recommendations
            </Button>
            <Button variant="outline" className="w-full">
              <Gift className="w-5 h-5 mr-2" />
              View Saved Gift Ideas
            </Button>
            <Button variant="ghost" className="w-full text-gray-500">
              Remind Me Later
            </Button>
          </div>

          {/* Settings Link */}
          <p className="text-xs text-center text-gray-500 mt-4">
            <span className="underline">Adjust reminder settings</span> for this date
          </p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
