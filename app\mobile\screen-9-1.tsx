import { Button } from "@/components/ui/button"
import { Sparkles, Calendar, Compass, User, Plus, ChevronRight } from "lucide-react"

export default function MobileScreen9_1() {
  const upcomingDates = [
    {
      name: "Mom's Birthday",
      date: "June 15, 2025",
      daysLeft: 12,
      type: "Birthday",
      person: "Mom",
      relationship: "Family",
    },
    {
      name: "Anniversary with <PERSON> Wei",
      date: "July 8, 2025",
      daysLeft: 35,
      type: "Anniversary",
      person: "<PERSON> Wei",
      relationship: "Partner",
    },
    {
      name: "Father's Day",
      date: "June 21, 2025",
      daysLeft: 18,
      type: "Holiday",
      person: "Dad",
      relationship: "Family",
    },
  ]

  const pastDates = [
    {
      name: "<PERSON>'s Birthday",
      date: "May 10, 2025",
      daysAgo: 24,
      type: "Birthday",
      person: "<PERSON>",
      relationship: "Friend",
    },
    {
      name: "Graduation Gift for Mei",
      date: "May 25, 2025",
      daysAgo: 9,
      type: "Graduation",
      person: "<PERSON>",
      relationship: "Friend",
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Header */}
      <div className="px-6 pt-6 pb-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900">My Dates</h1>
          <Button size="icon" variant="ghost" className="rounded-full">
            <Plus className="w-5 h-5 text-gray-600" />
          </Button>
        </div>
        <p className="text-gray-600">Never miss an important gift-giving occasion</p>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 pb-24 overflow-y-auto">
        {/* Upcoming Dates */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Dates</h2>
          <div className="space-y-3">
            {upcomingDates.map((date, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm flex items-center gap-4"
              >
                <div
                  className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    date.daysLeft < 14 ? "bg-red-100" : "bg-blue-100"
                  }`}
                >
                  <Calendar className={`w-6 h-6 ${date.daysLeft < 14 ? "text-red-600" : "text-blue-600"}`} />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{date.name}</h3>
                  <p className="text-sm text-gray-600">
                    {date.date} • {date.relationship}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <span className={`font-bold ${date.daysLeft < 14 ? "text-red-600" : "text-blue-600"}`}>
                    {date.daysLeft} days
                  </span>
                  <ChevronRight className="w-5 h-5 text-gray-400 mt-1" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Past Dates */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Past Dates</h2>
          <div className="space-y-3">
            {pastDates.map((date, index) => (
              <div
                key={index}
                className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm flex items-center gap-4"
              >
                <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-gray-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{date.name}</h3>
                  <p className="text-sm text-gray-600">
                    {date.date} • {date.relationship}
                  </p>
                </div>
                <div className="flex flex-col items-end">
                  <span className="font-medium text-gray-500">{date.daysAgo} days ago</span>
                  <ChevronRight className="w-5 h-5 text-gray-400 mt-1" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="absolute bottom-8 left-0 right-0 bg-white/90 backdrop-blur-sm mx-4 rounded-2xl border border-white/20">
        <div className="flex items-center justify-around py-3">
          <div className="flex flex-col items-center gap-1">
            <Sparkles className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">Recommend</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <Compass className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">Discover</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-white" />
            </div>
            <span className="text-xs font-medium text-orange-500">My Dates</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <User className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">Profile</span>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
