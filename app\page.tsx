"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Smartphone, Monitor, ChevronRight } from "lucide-react"

// Import screen components
import MobileScreen1_1 from "./mobile/screen-1-1"
import MobileScreen1_2 from "./mobile/screen-1-2"
import MobileScreen1_3 from "./mobile/screen-1-3"
import MobileScreen2_1 from "./mobile/screen-2-1"
import MobileScreen2_2 from "./mobile/screen-2-2"
import MobileScreen8_1 from "./mobile/screen-8-1"
import MobileScreen8_8 from "./mobile/screen-8-8"
import WebScreenIVA1 from "./web/screen-iv-a-1"
import WebScreenIVA3 from "./web/screen-iv-a-3"
import WebScreenIVC1 from "./web/screen-iv-c-1"
import MobileScreen1_4 from "./mobile/screen-1-4"
import MobileScreen1_5 from "./mobile/screen-1-5"
import MobileScreen1_6 from "./mobile/screen-1-6"
import MobileScreen1_7 from "./mobile/screen-1-7"
import MobileScreen1_8 from "./mobile/screen-1-8"
import MobileScreen2_4 from "./mobile/screen-2-4"
import MobileScreen2_5 from "./mobile/screen-2-5"
import MobileScreen4_1 from "./mobile/screen-4-1"
import MobileScreen6_1 from "./mobile/screen-6-1"
import MobileScreen2_3 from "./mobile/screen-2-3"
import MobileScreen3_1 from "./mobile/screen-3-1"
import MobileScreen3_2 from "./mobile/screen-3-2"
import MobileScreen6_2 from "./mobile/screen-6-2"
import MobileScreen7_1 from "./mobile/screen-7-1"
import MobileScreen8_3 from "./mobile/screen-8-3"
import MobileScreen5_1 from "./mobile/screen-5-1"
import MobileScreen5_2 from "./mobile/screen-5-2"
import MobileScreen5_3 from "./mobile/screen-5-3"
import MobileScreen6_3 from "./mobile/screen-6-3"
import MobileScreen6_4 from "./mobile/screen-6-4"
import MobileScreen8_5 from "./mobile/screen-8-5"
import MobileScreen8_6 from "./mobile/screen-8-6"
import MobileScreen8_9 from "./mobile/screen-8-9"
import MobileScreen9_1 from "./mobile/screen-9-1"
import MobileScreen9_2 from "./mobile/screen-9-2"
import MobileScreen9_3 from "./mobile/screen-9-3"
import MobileScreen9_4 from "./mobile/screen-9-4"
import MobileScreen10_1 from "./mobile/screen-10-1"
import MobileScreen10_2 from "./mobile/screen-10-2"
import MobileScreen10_3 from "./mobile/screen-10-3"
// Add missing mobile screen imports
import MobileScreen8_2 from "./mobile/screen-8-2"
import MobileScreen8_4 from "./mobile/screen-8-4"
import MobileScreen8_7 from "./mobile/screen-8-7"
import MobileScreen8_10 from "./mobile/screen-8-10"
import MobileScreen8_11 from "./mobile/screen-8-11"
import MobileScreen8_12 from "./mobile/screen-8-12"
import MobileScreen8_13 from "./mobile/screen-8-13"
import MobileScreen8_14 from "./mobile/screen-8-14"

// Add missing web screen imports
import WebScreenIVA2 from "./web/screen-iv-a-2"
import WebScreenIVA4 from "./web/screen-iv-a-4"
import WebScreenIVA5 from "./web/screen-iv-a-5"
import WebScreenIVA6 from "./web/screen-iv-a-6"
import WebScreenIVA7 from "./web/screen-iv-a-7"
import WebScreenIVA8 from "./web/screen-iv-a-8"
import WebScreenIVB1 from "./web/screen-iv-b-1"
import WebScreenIVB2 from "./web/screen-iv-b-2"
import WebScreenIVB3 from "./web/screen-iv-b-3"
import WebScreenIVC2 from "./web/screen-iv-c-2"
import WebScreenIVC3 from "./web/screen-iv-c-3"
import WebScreenIVC4 from "./web/screen-iv-c-4"
import WebScreenIVC5 from "./web/screen-iv-c-5"
import WebScreenIVC6 from "./web/screen-iv-c-6"
import WebScreenIVD1 from "./web/screen-iv-d-1"
import WebScreenIVD2 from "./web/screen-iv-d-2"
import WebScreenIVD3 from "./web/screen-iv-d-3"
import WebScreenIVD4 from "./web/screen-iv-d-4"
import WebScreenIVD5 from "./web/screen-iv-d-5"
import WebScreenIVD6 from "./web/screen-iv-d-6"
import WebScreenIVD7 from "./web/screen-iv-d-7"

interface ScreenItem {
  id: string
  title: string
  component: React.ComponentType
  type: "mobile" | "web"
}

const screens: ScreenItem[] = [
  // Mobile App Screens - Function 1: Guided Gifting Context Input
  { id: "1.1", title: "Mobile Screen 1.1: Home Screen / Start Page", component: MobileScreen1_1, type: "mobile" },
  {
    id: "1.2",
    title: "Mobile Screen 1.2: Context Input - Occasion Selection - Default",
    component: MobileScreen1_2,
    type: "mobile",
  },
  {
    id: "1.3",
    title: "Mobile Screen 1.3: Context Input - Occasion Selection - Occasion Selected",
    component: MobileScreen1_3,
    type: "mobile",
  },
  {
    id: "1.4",
    title: "Mobile Screen 1.4: Context Input - Relationship Definition - Default",
    component: MobileScreen1_4,
    type: "mobile",
  },
  {
    id: "1.5",
    title: "Mobile Screen 1.5: Context Input - Relationship Definition - Relationship Selected",
    component: MobileScreen1_5,
    type: "mobile",
  },
  {
    id: "1.6",
    title: "Mobile Screen 1.6: Context Input - Budget Range Specification - Default",
    component: MobileScreen1_6,
    type: "mobile",
  },
  {
    id: "1.7",
    title: "Mobile Screen 1.7: Context Input - Budget Range Specification - Budget Set",
    component: MobileScreen1_7,
    type: "mobile",
  },
  {
    id: "1.8",
    title: "Mobile Screen 1.8: Context Input - Recipient Profile Input",
    component: MobileScreen1_8,
    type: "mobile",
  },

  // Function 2: AI-Powered Personalized Gift Recommendations
  {
    id: "2.1",
    title: "Mobile Screen 2.1: Recommendation List - Loading State",
    component: MobileScreen2_1,
    type: "mobile",
  },
  {
    id: "2.2",
    title: "Mobile Screen 2.2: Recommendation List - Default View",
    component: MobileScreen2_2,
    type: "mobile",
  },
  {
    id: "2.3",
    title: "Mobile Screen 2.3: Recommendation List - Item Saved Feedback (Action on 2.2)",
    component: MobileScreen2_3,
    type: "mobile",
  },
  {
    id: "2.4",
    title: "Mobile Screen 2.4: Recommendation List - Search Active",
    component: MobileScreen2_4,
    type: "mobile",
  },
  {
    id: "2.5",
    title: "Mobile Screen 2.5: Recommendation List - Search Results",
    component: MobileScreen2_5,
    type: "mobile",
  },

  // Function 3: Social Persona Analysis
  {
    id: "3.1",
    title: "Mobile Screen 3.1: Social Profile Consent (Modal/Popup)",
    component: MobileScreen3_1,
    type: "mobile",
  },
  {
    id: "3.2",
    title: "Mobile Screen 3.2: Social Profile Analysis - Processing (Overlay/Indicator)",
    component: MobileScreen3_2,
    type: "mobile",
  },

  // Function 4: Gift Detail View
  { id: "4.1", title: "Mobile Screen 4.1: Gift Detail View", component: MobileScreen4_1, type: "mobile" },

  // Function 5: Partner E-commerce Redirection
  {
    id: "5.1",
    title: "Mobile Screen 5.1: Redirection - Loading/Transition (Overlay)",
    component: MobileScreen5_1,
    type: "mobile",
  },
  {
    id: "5.2",
    title: "Mobile Screen 5.2: Redirection - Intermediate Message (Toast)",
    component: MobileScreen5_2,
    type: "mobile",
  },
  {
    id: "5.3",
    title: "Mobile Screen 5.3: Redirection - Error State (Message/Alert)",
    component: MobileScreen5_3,
    type: "mobile",
  },

  // Function 6: Discover / Inspiration Section
  { id: "6.1", title: "Mobile Screen 6.1: Discover Section - Main View", component: MobileScreen6_1, type: "mobile" },
  {
    id: "6.2",
    title: "Mobile Screen 6.2: Discover Section - Collection Gift List",
    component: MobileScreen6_2,
    type: "mobile",
  },
  {
    id: "6.3",
    title: "Mobile Screen 6.3: Discover Section - Search Active",
    component: MobileScreen6_3,
    type: "mobile",
  },
  {
    id: "6.4",
    title: "Mobile Screen 6.4: Discover Section - Search Results",
    component: MobileScreen6_4,
    type: "mobile",
  },

  // Function 7: Search & Filtering
  {
    id: "7.1",
    title: "Mobile Screen 7.1: Filter Panel (Modal/Bottom Sheet)",
    component: MobileScreen7_1,
    type: "mobile",
  },

  // Function 8: User Account & Profile Management
  { id: "8.1", title: "Mobile Screen 8.1: Registration - Default", component: MobileScreen8_1, type: "mobile" },
  {
    id: "8.2",
    title: "Mobile Screen 8.2: Registration - Input Validation Error (State of 8.1)",
    component: MobileScreen8_2,
    type: "mobile",
  },
  { id: "8.3", title: "Mobile Screen 8.3: Login - Default", component: MobileScreen8_3, type: "mobile" },
  {
    id: "8.4",
    title: "Mobile Screen 8.4: Login - Input Validation Error (State of 8.3)",
    component: MobileScreen8_4,
    type: "mobile",
  },
  {
    id: "8.5",
    title: "Mobile Screen 8.5: Password Reset - Step 1: Enter Email",
    component: MobileScreen8_5,
    type: "mobile",
  },
  {
    id: "8.6",
    title: "Mobile Screen 8.6: Password Reset - Step 2: Confirmation Message",
    component: MobileScreen8_6,
    type: "mobile",
  },
  {
    id: "8.7",
    title: "Mobile Screen 8.7: Password Reset - Step 3: Create New Password",
    component: MobileScreen8_7,
    type: "mobile",
  },
  { id: "8.8", title: "Mobile Screen 8.8: User Profile - Main View", component: MobileScreen8_8, type: "mobile" },
  {
    id: "8.9",
    title: "Mobile Screen 8.9: User Profile - Edit Profile Screen",
    component: MobileScreen8_9,
    type: "mobile",
  },
  {
    id: "8.10",
    title: "Mobile Screen 8.10: User Profile - Wishlist/Favorites View",
    component: MobileScreen8_10,
    type: "mobile",
  },
  {
    id: "8.11",
    title: "Mobile Screen 8.11: User Profile - Wishlist/Favorites - Empty State",
    component: MobileScreen8_11,
    type: "mobile",
  },
  {
    id: "8.12",
    title: "Mobile Screen 8.12: User Profile - Settings - Linked Social Accounts",
    component: MobileScreen8_12,
    type: "mobile",
  },
  {
    id: "8.13",
    title: "Mobile Screen 8.13: User Profile - Feedback Form",
    component: MobileScreen8_13,
    type: "mobile",
  },
  {
    id: "8.14",
    title: "Mobile Screen 8.14: User Profile - App Rating Prompt (Modal)",
    component: MobileScreen8_14,
    type: "mobile",
  },

  // Function 9: Important Date Reminders
  { id: "9.1", title: "Mobile Screen 9.1: Date Reminders - List View", component: MobileScreen9_1, type: "mobile" },
  {
    id: "9.2",
    title: "Mobile Screen 9.2: Date Reminders - List View - Empty State",
    component: MobileScreen9_2,
    type: "mobile",
  },
  {
    id: "9.3",
    title: "Mobile Screen 9.3: Date Reminders - Add/Edit Reminder Screen",
    component: MobileScreen9_3,
    type: "mobile",
  },
  {
    id: "9.4",
    title: "Mobile Screen 9.4: Date Reminder Notification (In-App Simulation)",
    component: MobileScreen9_4,
    type: "mobile",
  },

  // Function 10: E-Card Design & Sending
  { id: "10.1", title: "Mobile Screen 10.1: E-Card - Template Library", component: MobileScreen10_1, type: "mobile" },
  { id: "10.2", title: "Mobile Screen 10.2: E-Card - Editor Screen", component: MobileScreen10_2, type: "mobile" },
  {
    id: "10.3",
    title: "Mobile Screen 10.3: E-Card - Preview & Share Screen",
    component: MobileScreen10_3,
    type: "mobile",
  },

  // Web Portal Screens
  // Function IV.A: Merchant/Brand Onboarding & Product Feed Management
  { id: "IV.A.1", title: "Web Screen IV.A.1: Partner Registration Page", component: WebScreenIVA1, type: "web" },
  { id: "IV.A.2", title: "Web Screen IV.A.2: Partner Login Page", component: WebScreenIVA2, type: "web" },
  { id: "IV.A.3", title: "Web Screen IV.A.3: Partner Dashboard (Post-Login)", component: WebScreenIVA3, type: "web" },
  {
    id: "IV.A.4",
    title: "Web Screen IV.A.4: Product Management - Product List View",
    component: WebScreenIVA4,
    type: "web",
  },
  {
    id: "IV.A.5",
    title: "Web Screen IV.A.5: Product Management - Add/Edit Product Form",
    component: WebScreenIVA5,
    type: "web",
  },
  {
    id: "IV.A.6",
    title: "Web Screen IV.A.6: Product Management - Bulk Upload / API Integration Guide",
    component: WebScreenIVA6,
    type: "web",
  },
  {
    id: "IV.A.7",
    title: "Web Screen IV.A.7: Validation & Quality Control Interface",
    component: WebScreenIVA7,
    type: "web",
  },
  {
    id: "IV.A.8",
    title: "Web Screen IV.A.8: Partner Account Settings",
    component: WebScreenIVA8,
    type: "web",
  },

  // Function IV.B: Partnership Performance Analytics Dashboard
  {
    id: "IV.B.1",
    title: "Web Screen IV.B.1: Analytics Dashboard Overview",
    component: WebScreenIVB1,
    type: "web",
  },
  {
    id: "IV.B.2",
    title: "Web Screen IV.B.2: Detailed Reports Section",
    component: WebScreenIVB2,
    type: "web",
  },
  {
    id: "IV.B.3",
    title: "Web Screen IV.B.3: Commission/Revenue Share Reports (If Applicable)",
    component: WebScreenIVB3,
    type: "web",
  },

  // Function IV.C: AI Model Training & Management Interface
  { id: "IV.C.1", title: "Web Screen IV.C.1: AI Model Performance Dashboard", component: WebScreenIVC1, type: "web" },
  {
    id: "IV.C.2",
    title: "Web Screen IV.C.2: Data Source Management",
    component: WebScreenIVC2,
    type: "web",
  },
  {
    id: "IV.C.3",
    title: "Web Screen IV.C.3: Model Versioning & Deployment Control (Model Registry)",
    component: WebScreenIVC3,
    type: "web",
  },
  {
    id: "IV.C.4",
    title: "Web Screen IV.C.4: A/B Testing Management",
    component: WebScreenIVC4,
    type: "web",
  },
  {
    id: "IV.C.5",
    title: "Web Screen IV.C.5: Retraining Scheduler & Logs (Training Jobs)",
    component: WebScreenIVC5,
    type: "web",
  },
  {
    id: "IV.C.6",
    title: "Web Screen IV.C.6: Feedback Loop Integration",
    component: WebScreenIVC6,
    type: "web",
  },

  // Function IV.D: Content Management System
  {
    id: "IV.D.1",
    title: "Web Screen IV.D.1: CMS Dashboard / Content Overview",
    component: WebScreenIVD1,
    type: "web",
  },
  {
    id: "IV.D.2",
    title: "Web Screen IV.D.2: Content Creation/Editing Interface",
    component: WebScreenIVD2,
    type: "web",
  },
  {
    id: "IV.D.3",
    title: "Web Screen IV.D.3: Category Management",
    component: WebScreenIVD3,
    type: "web",
  },
  {
    id: "IV.D.4",
    title: "Web Screen IV.D.4: Media Library",
    component: WebScreenIVD4,
    type: "web",
  },
  {
    id: "IV.D.5",
    title: "Web Screen IV.D.5: Scheduling & Publishing Controls (Overview)",
    component: WebScreenIVD5,
    type: "web",
  },
  {
    id: "IV.D.6",
    title: "Web Screen IV.D.6: Content Preview Functionality (State of IV.D.2)",
    component: WebScreenIVD6,
    type: "web",
  },
  {
    id: "IV.D.7",
    title: "Web Screen IV.D.7: User Role Management (CMS Users)",
    component: WebScreenIVD7,
    type: "web",
  },
]

export default function MasterNavigation() {
  const [selectedScreen, setSelectedScreen] = useState<string | null>(null)

  const selectedScreenData = screens.find((screen) => screen.id === selectedScreen)

  if (selectedScreen && selectedScreenData) {
    const ScreenComponent = selectedScreenData.component
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b px-4 py-3 flex items-center gap-3">
          <Button variant="outline" size="sm" onClick={() => setSelectedScreen(null)}>
            ← Back to Directory
          </Button>
          <h1 className="text-lg font-semibold">{selectedScreenData.title}</h1>
          <Badge variant={selectedScreenData.type === "mobile" ? "default" : "secondary"}>
            {selectedScreenData.type === "mobile" ? "Mobile" : "Web"}
          </Badge>
        </div>
        <div className="p-4">
          <ScreenComponent />
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white border-b px-6 py-4">
        <h1 className="text-2xl font-bold text-gray-900">GiftSpark AI - Unified Atomic Screens Directory</h1>
        <p className="text-gray-600 mt-1">Tap on any item to view the corresponding atomic screen prototype</p>
      </div>

      <div className="max-w-6xl mx-auto p-6 space-y-8">
        {/* Mobile App Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="w-5 h-5" />
              Mobile App - User Facing (iPhone 16 Pro Max)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 在JSX中添加更多功能分组 */}
            <div>
              <h3 className="font-semibold text-lg mb-3">Function 1: Guided Gifting Context Input</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("1."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 2: AI-Powered Personalized Gift Recommendations</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("2."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 3: Social Persona Analysis (Optional Integration)</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("3."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 4: Gift Detail View</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("4."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 5: Partner E-commerce Redirection</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("5."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 6: Discover / Inspiration Section</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("6."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 7: Search & Filtering (Global Tool)</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("7."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 8: User Account & Profile Management</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("8."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 9: Important Date Reminders</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("9."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function 10: E-Card Design & Sending</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("10."))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Web Portal Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Web Portals & Internal Tools (Desktop Browser)
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 在Web Portal部分添加更多功能分组 */}
            <div>
              <h3 className="font-semibold text-lg mb-3">
                Function IV.A: Merchant/Brand Onboarding & Product Feed Management
              </h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("IV.A"))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function IV.B: Partnership Performance Analytics Dashboard</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("IV.B"))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function IV.C: AI Model Training & Management Interface</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("IV.C"))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-lg mb-3">Function IV.D: Content Management System (CMS)</h3>
              <div className="space-y-2">
                {screens
                  .filter((s) => s.id.startsWith("IV.D"))
                  .map((screen) => (
                    <Button
                      key={screen.id}
                      variant="ghost"
                      className="w-full justify-between h-auto p-3 text-left"
                      onClick={() => setSelectedScreen(screen.id)}
                    >
                      <span className="text-sm">{screen.title}</span>
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-sm text-gray-500 py-4">
          Tap on any item to view the corresponding atomic screen prototype. This page is for review purposes only.
        </div>
      </div>
    </div>
  )
}
