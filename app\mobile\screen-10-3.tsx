"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, Mail, User, Calendar, Clock, Send } from "lucide-react"
import { useState } from "react"

export default function MobileScreen10_3() {
  const [recipient, setRecipient] = useState("")
  const [subject, setSubject] = useState("Happy Birthday!")
  const [message, setMessage] = useState("Wishing you a wonderful birthday filled with joy and happiness!")
  const [scheduleDate, setScheduleDate] = useState("")

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Send E-Card</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Steps */}
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 3: Send E-Card</p>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-4 pb-24 overflow-y-auto">
        {/* Card Preview */}
        <div className="mb-6">
          <h2 className="text-sm font-medium text-gray-700 mb-3">Card Preview</h2>
          <div className="bg-gray-50 rounded-xl p-4 flex justify-center">
            <div className="w-48 h-64 bg-white rounded-lg shadow-md overflow-hidden">
              <img
                src="/placeholder.svg?height=120&width=120&text=Birthday+Card"
                alt="Card Preview"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* Recipient Information */}
        <div className="mb-6 space-y-4">
          <h2 className="text-sm font-medium text-gray-700">Recipient Information</h2>

          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type="email"
              placeholder="Recipient's Email"
              value={recipient}
              onChange={(e) => setRecipient(e.target.value)}
              className="pl-12 h-12"
            />
          </div>

          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input placeholder="Recipient's Name (Optional)" className="pl-12 h-12" />
          </div>
        </div>

        {/* Message */}
        <div className="mb-6 space-y-4">
          <h2 className="text-sm font-medium text-gray-700">Message</h2>

          <Input placeholder="Subject" value={subject} onChange={(e) => setSubject(e.target.value)} className="h-12" />

          <Textarea
            placeholder="Your message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="min-h-[120px] resize-none"
          />
        </div>

        {/* Delivery Options */}
        <div className="mb-6">
          <h2 className="text-sm font-medium text-gray-700 mb-3">Delivery Options</h2>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Calendar className="w-5 h-5 text-gray-600" />
                <span className="text-gray-900">Schedule Delivery</span>
              </div>
              <div className="relative w-32">
                <Input
                  type="date"
                  value={scheduleDate}
                  onChange={(e) => setScheduleDate(e.target.value)}
                  className="h-9 text-sm"
                />
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-gray-600" />
                <span className="text-gray-900">Delivery Time</span>
              </div>
              <select className="h-9 rounded-md border border-gray-200 bg-white px-3 text-sm">
                <option>9:00 AM</option>
                <option>12:00 PM</option>
                <option>3:00 PM</option>
                <option>6:00 PM</option>
              </select>
            </div>
          </div>
        </div>

        {/* Sender Information */}
        <div className="mb-6">
          <h2 className="text-sm font-medium text-gray-700 mb-3">From</h2>
          <Input placeholder="Your Name" defaultValue="Jeson Chen" className="h-12" />
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
          <Send className="w-5 h-5 mr-2" />
          Send E-Card
        </Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Save as Draft
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
