import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Search, Gift, Heart, Calendar, Cake, Star, Coffee, Check } from "lucide-react"

export default function MobileScreen1_3() {
  const occasions = [
    { name: "Birthday", icon: Cake, popular: true, selected: true },
    { name: "Anniversary", icon: Heart, popular: false, selected: false },
    { name: "Thank You", icon: Star, popular: false, selected: false },
    { name: "Christmas", icon: Gift, popular: true, selected: false },
    { name: "Valentine's Day", icon: Heart, popular: true, selected: false },
    { name: "Chinese New Year", icon: Calendar, popular: true, selected: false },
    { name: "Graduation", icon: Star, popular: false, selected: false },
    { name: "Housewarming", icon: Coffee, popular: false, selected: false },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">What's the occasion?</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Indicator */}
      <div className="px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 1 of 4</p>
      </div>

      {/* Search Bar */}
      <div className="px-4 mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input placeholder="Search for an occasion" className="pl-10 h-12 bg-gray-50 border-0" />
        </div>
      </div>

      {/* Occasions List */}
      <div className="flex-1 px-4 pb-24">
        <div className="space-y-3">
          {occasions.map((occasion, index) => {
            const IconComponent = occasion.icon
            return (
              <div
                key={index}
                className={`flex items-center gap-4 p-4 rounded-xl border transition-colors ${
                  occasion.selected ? "bg-orange-50 border-orange-200" : "bg-gray-50 border-gray-100 hover:bg-gray-100"
                }`}
              >
                <div
                  className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                    occasion.selected ? "bg-orange-100" : "bg-white"
                  }`}
                >
                  <IconComponent className={`w-6 h-6 ${occasion.selected ? "text-orange-600" : "text-orange-500"}`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className={`font-medium ${occasion.selected ? "text-orange-900" : "text-gray-900"}`}>
                      {occasion.name}
                    </span>
                    {occasion.popular && (
                      <span className="px-2 py-1 bg-orange-100 text-orange-600 text-xs font-medium rounded-full">
                        Popular
                      </span>
                    )}
                  </div>
                </div>
                {occasion.selected && (
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
            )
          })}

          {/* Other Option */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl border border-gray-100">
            <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center">
              <span className="text-lg">✨</span>
            </div>
            <span className="font-medium text-gray-900">Other</span>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">Next</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Skip / Fill Later
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
