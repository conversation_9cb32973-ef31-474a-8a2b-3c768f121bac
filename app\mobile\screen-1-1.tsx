"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Sparkles, Gift, Heart, Search, User, Calendar } from "lucide-react"
import { IPhoneFrame } from "@/components/iphone-frame"

export default function MobileScreen1_1() {
  return (
    <IPhoneFrame>
      <div className="flex flex-col h-full bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
        {/* Header */}
        <div className="px-6 pt-6 pb-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
                <Sparkles className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">GiftSpark</h1>
                <p className="text-sm text-gray-600">AI-Powered Gift Discovery</p>
              </div>
            </div>
            <Button variant="ghost" size="sm" className="rounded-full">
              <User className="w-5 h-5" />
            </Button>
          </div>

          {/* Welcome Message */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-sm">
            <h2 className="text-lg font-semibold text-gray-900 mb-2">Find the Perfect Gift ✨</h2>
            <p className="text-gray-600 text-sm">Let our AI help you discover meaningful gifts for your loved ones</p>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 px-6 space-y-6">
          {/* Quick Start */}
          <Card className="bg-gradient-to-r from-purple-600 to-pink-600 border-0 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold mb-2">Start Gift Hunt</h3>
                  <p className="text-purple-100 text-sm mb-4">Tell us about the occasion and recipient</p>
                  <Button className="bg-white text-purple-600 hover:bg-purple-50" size="sm">
                    Get Started
                  </Button>
                </div>
                <Gift className="w-12 h-12 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-white/80 backdrop-blur-sm border-white/20">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Search className="w-6 h-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">Discover</h4>
                  <p className="text-xs text-gray-600">Browse trending gifts</p>
                </CardContent>
              </Card>

              <Card className="bg-white/80 backdrop-blur-sm border-white/20">
                <CardContent className="p-4 text-center">
                  <div className="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Heart className="w-6 h-6 text-red-600" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">Wishlist</h4>
                  <p className="text-xs text-gray-600">Saved favorites</p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Activity */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Searches</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-white/80 backdrop-blur-sm rounded-xl border border-white/20">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Gift className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 text-sm">Birthday Gift</p>
                    <p className="text-xs text-gray-600">For Mom • $50-100</p>
                  </div>
                </div>
                <Badge variant="secondary" className="text-xs">
                  3 results
                </Badge>
              </div>

              <div className="flex items-center justify-between p-3 bg-white/80 backdrop-blur-sm rounded-xl border border-white/20">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Heart className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 text-sm">Anniversary</p>
                    <p className="text-xs text-gray-600">For Partner • $100-200</p>
                  </div>
                </div>
                <Badge variant="secondary" className="text-xs">
                  5 results
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Navigation */}
        <div className="px-6 py-4 bg-white/90 backdrop-blur-sm border-t border-white/20">
          <div className="flex items-center justify-around">
            <Button variant="ghost" size="sm" className="flex flex-col items-center space-y-1">
              <Gift className="w-5 h-5 text-purple-600" />
              <span className="text-xs text-purple-600 font-medium">Home</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex flex-col items-center space-y-1">
              <Search className="w-5 h-5 text-gray-400" />
              <span className="text-xs text-gray-400">Discover</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex flex-col items-center space-y-1">
              <Heart className="w-5 h-5 text-gray-400" />
              <span className="text-xs text-gray-400">Wishlist</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex flex-col items-center space-y-1">
              <Calendar className="w-5 h-5 text-gray-400" />
              <span className="text-xs text-gray-400">Dates</span>
            </Button>
            <Button variant="ghost" size="sm" className="flex flex-col items-center space-y-1">
              <User className="w-5 h-5 text-gray-400" />
              <span className="text-xs text-gray-400">Profile</span>
            </Button>
          </div>
        </div>
      </div>
    </IPhoneFrame>
  )
}
