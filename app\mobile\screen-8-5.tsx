import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Mail, Sparkles } from "lucide-react"

export default function MobileScreen8_5() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Reset Password</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8">
        {/* App Logo */}
        <div className="flex items-center justify-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-pink-500 rounded-2xl flex items-center justify-center">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Forgot Password?</h1>
        <p className="text-gray-600 text-center mb-8 leading-relaxed">
          No worries! Enter your email address and we'll send you a link to reset your password.
        </p>

        {/* Form */}
        <div className="space-y-6 mb-8">
          {/* Email */}
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input type="email" placeholder="Enter your email address" className="pl-12 h-12 bg-gray-50 border-0" />
          </div>
        </div>

        {/* Send Reset Link Button */}
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold mb-6">
          Send Reset Link
        </Button>

        {/* Back to Login */}
        <div className="text-center">
          <span className="text-sm text-gray-600">
            Remember your password? <span className="text-orange-500 font-medium underline">Back to Login</span>
          </span>
        </div>

        {/* Help Text */}
        <div className="mt-8 p-4 bg-blue-50 rounded-xl border border-blue-100">
          <h4 className="font-medium text-blue-900 mb-2">💡 Need Help?</h4>
          <p className="text-sm text-blue-800 leading-relaxed">
            If you don't receive the email within a few minutes, check your spam folder or contact our support team.
          </p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
