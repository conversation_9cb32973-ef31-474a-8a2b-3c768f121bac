import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Heart, Star, ExternalLink } from "lucide-react"

export default function MobileScreen6_2() {
  const giftCollection = [
    {
      id: 1,
      name: "Minimalist Desk Organizer",
      price: "¥89-129",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.4,
      brand: "CleanSpace",
      tags: ["Productivity", "Minimalist"],
    },
    {
      id: 2,
      name: "Bamboo Phone Stand",
      price: "¥45-65",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.2,
      brand: "EcoTech",
      tags: ["Eco-friendly", "Tech"],
    },
    {
      id: 3,
      name: "LED Reading Light",
      price: "¥78-98",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.6,
      brand: "BrightLife",
      tags: ["Reading", "Lighting"],
    },
    {
      id: 4,
      name: "Ceramic Coffee Mug Set",
      price: "¥120-160",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.5,
      brand: "CoffeeCraft",
      tags: ["Coffee", "Ceramic"],
    },
    {
      id: 5,
      name: "Wireless Charging Pad",
      price: "¥99-139",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.3,
      brand: "PowerFlow",
      tags: ["Tech", "Wireless"],
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Work From Home Essentials</h1>
        <div className="w-6"></div>
      </div>

      {/* Collection Header */}
      <div className="px-4 py-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Work From Home Essentials</h2>
        <p className="text-sm text-gray-600">Perfect gifts for the remote worker in your life</p>
        <div className="flex items-center gap-2 mt-2">
          <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">Trending</span>
          <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">Curated</span>
        </div>
      </div>

      {/* Gift List */}
      <div className="flex-1 px-4 py-4 pb-24 space-y-4">
        {giftCollection.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
            <div className="flex gap-4">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-20 h-20 rounded-lg object-cover bg-gray-100"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm leading-tight">{item.name}</h3>
                  <Heart className="w-5 h-5 text-gray-400 ml-2 flex-shrink-0" />
                </div>

                <p className="text-lg font-semibold text-gray-900 mb-1">{item.price}</p>

                <div className="flex items-center gap-1 mb-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{item.rating}</span>
                  <span className="text-sm text-gray-400">• {item.brand}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-3">
                  {item.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>

                <Button size="sm" className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4">
        <Button variant="outline" className="w-full">
          View More Collections
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
