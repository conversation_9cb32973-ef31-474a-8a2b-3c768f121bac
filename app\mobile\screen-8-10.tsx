import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Heart, Calendar, Gift, Star, Trash2 } from "lucide-react"

export default function MobileScreen8_10() {
  const wishlistItems = [
    {
      id: 1,
      name: "Wireless Bluetooth Headphones",
      price: "¥299-399",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.5,
      brand: "TechSound",
      savedDate: "2 days ago",
    },
    {
      id: 2,
      name: "Artisan Coffee Subscription",
      price: "¥180/month",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.8,
      brand: "BrewMaster",
      savedDate: "1 week ago",
    },
    {
      id: 3,
      name: "Personalized Photo Album",
      price: "¥120-200",
      image: "/placeholder.svg?height=80&width=80",
      rating: 4.6,
      brand: "MemoryKeep",
      savedDate: "2 weeks ago",
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">My Wishlist</h1>
        <span className="text-orange-500 font-medium">Edit</span>
      </div>

      {/* Stats */}
      <div className="px-4 py-4 bg-orange-50 border-b border-orange-100">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Saved Gifts</h2>
            <p className="text-sm text-gray-600">{wishlistItems.length} items in your wishlist</p>
          </div>
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
            <Heart className="w-6 h-6 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Wishlist Items */}
      <div className="flex-1 px-4 py-4 pb-24 space-y-4">
        {wishlistItems.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
            <div className="flex gap-4">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-20 h-20 rounded-lg object-cover bg-gray-100"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm leading-tight">{item.name}</h3>
                  <button className="ml-2">
                    <Trash2 className="w-4 h-4 text-gray-400" />
                  </button>
                </div>

                <p className="text-lg font-semibold text-gray-900 mb-1">{item.price}</p>

                <div className="flex items-center gap-1 mb-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{item.rating}</span>
                  <span className="text-sm text-gray-400">• {item.brand}</span>
                </div>

                <p className="text-xs text-gray-500 mb-3">Saved {item.savedDate}</p>

                <div className="flex gap-2">
                  <Button size="sm" className="flex-1 bg-orange-500 hover:bg-orange-600 text-white">
                    View Details
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    Buy Now
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Empty State (if no items) */}
        {wishlistItems.length === 0 && (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Heart className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Your wishlist is empty</h3>
            <p className="text-gray-600 text-center mb-6">
              Start saving gifts you love to keep track of them for later.
            </p>
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              <Gift className="w-4 h-4 mr-2" />
              Discover Gifts
            </Button>
          </div>
        )}
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4">
        <Button variant="outline" className="w-full">
          <Calendar className="w-5 h-5 mr-2" />
          Create Gift Reminder
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
