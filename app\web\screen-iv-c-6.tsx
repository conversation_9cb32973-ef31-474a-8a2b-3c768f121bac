"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Save, Users, TrendingUp } from "lucide-react"
import Link from "next/link"

export default function WebScreenIVC6() {
  const [seoSettings, setSeoSettings] = useState({
    siteName: "GiftSpark",
    siteDescription: "AI-powered gift recommendation platform",
    defaultTitle: "GiftSpark - Find Perfect Gifts with AI",
    defaultDescription:
      "Discover personalized gift recommendations powered by AI. Find the perfect gift for any occasion.",
    keywords: "gifts, AI recommendations, personalized gifts, gift ideas",
    ogImage: "",
    twitterCard: "summary_large_image",
    googleAnalytics: "",
    googleSearchConsole: "",
    facebookPixel: "",
  })

  const [socialMedia, setSocialMedia] = useState({
    facebook: "https://facebook.com/giftspark",
    twitter: "https://twitter.com/giftspark",
    instagram: "https://instagram.com/giftspark",
    linkedin: "https://linkedin.com/company/giftspark",
  })

  const [contentSettings, setContentSettings] = useState({
    autoPublish: false,
    moderationRequired: true,
    allowComments: true,
    enableRatings: true,
    maxFileSize: "10",
    allowedFileTypes: ["jpg", "png", "gif", "pdf"],
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/web/screen-iv-c-1">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Content
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">Content Settings</h1>
              <p className="text-gray-600">Configure content management settings</p>
            </div>
          </div>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Save Settings
          </Button>
        </div>
      </div>

      <div className="p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          {/* SEO Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                SEO Configuration
              </CardTitle>
              <CardDescription>Configure search engine optimization settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="site-name">Site Name</Label>
                  <Input
                    id="site-name"
                    value={seoSettings.siteName}
                    onChange={(e) => setSeoSettings({ ...seoSettings, siteName: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="default-title">Default Title</Label>
                  <Input
                    id="default-title"
                    value={seoSettings.defaultTitle}
                    onChange={(e) => setSeoSettings({ ...seoSettings, defaultTitle: e.target.value })}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="site-description">Site Description</Label>
                <Textarea
                  id="site-description"
                  value={seoSettings.siteDescription}
                  onChange={(e) => setSeoSettings({ ...seoSettings, siteDescription: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="default-description">Default Meta Description</Label>
                <Textarea
                  id="default-description"
                  value={seoSettings.defaultDescription}
                  onChange={(e) => setSeoSettings({ ...seoSettings, defaultDescription: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="keywords">Default Keywords</Label>
                <Input
                  id="keywords"
                  placeholder="Separate keywords with commas"
                  value={seoSettings.keywords}
                  onChange={(e) => setSeoSettings({ ...seoSettings, keywords: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="og-image">Default OG Image URL</Label>
                  <Input
                    id="og-image"
                    placeholder="https://example.com/og-image.jpg"
                    value={seoSettings.ogImage}
                    onChange={(e) => setSeoSettings({ ...seoSettings, ogImage: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="twitter-card">Twitter Card Type</Label>
                  <Select
                    value={seoSettings.twitterCard}
                    onValueChange={(value) => setSeoSettings({ ...seoSettings, twitterCard: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="summary">Summary</SelectItem>
                      <SelectItem value="summary_large_image">Summary Large Image</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analytics & Tracking */}
          <Card>
            <CardHeader>
              <CardTitle>Analytics & Tracking</CardTitle>
              <CardDescription>Configure analytics and tracking codes</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="google-analytics">Google Analytics ID</Label>
                  <Input
                    id="google-analytics"
                    placeholder="GA-XXXXXXXXX-X"
                    value={seoSettings.googleAnalytics}
                    onChange={(e) => setSeoSettings({ ...seoSettings, googleAnalytics: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="search-console">Google Search Console</Label>
                  <Input
                    id="search-console"
                    placeholder="Verification code"
                    value={seoSettings.googleSearchConsole}
                    onChange={(e) => setSeoSettings({ ...seoSettings, googleSearchConsole: e.target.value })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="facebook-pixel">Facebook Pixel ID</Label>
                <Input
                  id="facebook-pixel"
                  placeholder="Facebook Pixel ID"
                  value={seoSettings.facebookPixel}
                  onChange={(e) => setSeoSettings({ ...seoSettings, facebookPixel: e.target.value })}
                />
              </div>
            </CardContent>
          </Card>

          {/* Social Media */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Social Media Links
              </CardTitle>
              <CardDescription>Configure social media profiles</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="facebook">Facebook</Label>
                  <Input
                    id="facebook"
                    value={socialMedia.facebook}
                    onChange={(e) => setSocialMedia({ ...socialMedia, facebook: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="twitter">Twitter</Label>
                  <Input
                    id="twitter"
                    value={socialMedia.twitter}
                    onChange={(e) => setSocialMedia({ ...socialMedia, twitter: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="instagram">Instagram</Label>
                  <Input
                    id="instagram"
                    value={socialMedia.instagram}
                    onChange={(e) => setSocialMedia({ ...socialMedia, instagram: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="linkedin">LinkedIn</Label>
                  <Input
                    id="linkedin"
                    value={socialMedia.linkedin}
                    onChange={(e) => setSocialMedia({ ...socialMedia, linkedin: e.target.value })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Content Management Settings</CardTitle>
              <CardDescription>Configure content creation and moderation settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-publish">Auto-publish content</Label>
                    <Switch
                      id="auto-publish"
                      checked={contentSettings.autoPublish}
                      onCheckedChange={(checked) => setContentSettings({ ...contentSettings, autoPublish: checked })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="moderation">Require moderation</Label>
                    <Switch
                      id="moderation"
                      checked={contentSettings.moderationRequired}
                      onCheckedChange={(checked) =>
                        setContentSettings({ ...contentSettings, moderationRequired: checked })
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="comments">Allow comments</Label>
                    <Switch
                      id="comments"
                      checked={contentSettings.allowComments}
                      onCheckedChange={(checked) => setContentSettings({ ...contentSettings, allowComments: checked })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="ratings">Enable ratings</Label>
                    <Switch
                      id="ratings"
                      checked={contentSettings.enableRatings}
                      onCheckedChange={(checked) => setContentSettings({ ...contentSettings, enableRatings: checked })}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="max-file-size">Max file size (MB)</Label>
                    <Input
                      id="max-file-size"
                      type="number"
                      value={contentSettings.maxFileSize}
                      onChange={(e) => setContentSettings({ ...contentSettings, maxFileSize: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label>Allowed file types</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {contentSettings.allowedFileTypes.map((type) => (
                        <Badge key={type} variant="secondary">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
