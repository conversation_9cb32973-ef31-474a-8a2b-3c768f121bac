import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Filter, Heart, Star, ExternalLink, Check } from "lucide-react"

export default function MobileScreen2_3() {
  const recommendations = [
    {
      id: 1,
      name: "Wireless Bluetooth Headphones",
      price: "¥299-399",
      image: "/placeholder.svg?height=80&width=80",
      rationale: "Because they love music and tech",
      rating: 4.5,
      brand: "TechSound",
      saved: true,
    },
    {
      id: 2,
      name: "Artisan Coffee Subscription",
      price: "¥180/month",
      image: "/placeholder.svg?height=80&width=80",
      rationale: "Perfect for coffee enthusiasts",
      rating: 4.8,
      brand: "BrewMaster",
      saved: false,
    },
    {
      id: 3,
      name: "Personalized Photo Album",
      price: "¥120-200",
      image: "/placeholder.svg?height=80&width=80",
      rationale: "Great for preserving memories",
      rating: 4.6,
      brand: "MemoryKeep",
      saved: false,
    },
    {
      id: 4,
      name: "Succulent Plant Set",
      price: "¥80-150",
      image: "/placeholder.svg?height=80&width=80",
      rationale: "Low maintenance, perfect for beginners",
      rating: 4.3,
      brand: "GreenLife",
      saved: false,
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Gifts for Friend</h1>
        <Filter className="w-6 h-6 text-gray-600" />
      </div>

      {/* Context Summary */}
      <div className="px-4 py-3 bg-orange-50 border-b border-orange-100">
        <p className="text-sm text-orange-800">
          <span className="font-medium">Birthday</span> • <span className="font-medium">Friend</span> •{" "}
          <span className="font-medium">¥50-100</span>
        </p>
      </div>

      {/* Results Count */}
      <div className="px-4 py-3">
        <p className="text-sm text-gray-600">Found 12 recommendations</p>
      </div>

      {/* Success Toast */}
      <div className="absolute top-20 left-4 right-4 z-50">
        <div className="bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center gap-3 animate-in slide-in-from-top duration-300">
          <Check className="w-5 h-5" />
          <span className="text-sm font-medium">Added to wishlist!</span>
        </div>
      </div>

      {/* Recommendations List */}
      <div className="flex-1 px-4 pb-32 space-y-4">
        {recommendations.map((item) => (
          <div key={item.id} className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
            <div className="flex gap-4">
              <img
                src={item.image || "/placeholder.svg"}
                alt={item.name}
                className="w-20 h-20 rounded-lg object-cover bg-gray-100"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-gray-900 text-sm leading-tight">{item.name}</h3>
                  <Heart
                    className={`w-5 h-5 ml-2 flex-shrink-0 ${item.saved ? "text-red-500 fill-current" : "text-gray-400"}`}
                  />
                </div>

                <p className="text-lg font-semibold text-gray-900 mb-1">{item.price}</p>

                <div className="flex items-center gap-1 mb-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{item.rating}</span>
                  <span className="text-sm text-gray-400">• {item.brand}</span>
                </div>

                <div className="bg-blue-50 rounded-lg p-2 mb-3">
                  <p className="text-xs text-blue-800">
                    <span className="font-medium">Why this gift?</span> {item.rationale}
                  </p>
                </div>

                <Button size="sm" className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button variant="outline" className="w-full">
          Refine Search / Modify Criteria
        </Button>
        <p className="text-center text-sm text-gray-500">
          Not satisfied? Try our <span className="text-orange-500 underline">'Discover' section</span>
        </p>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
