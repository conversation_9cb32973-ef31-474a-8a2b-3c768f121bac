import { ExternalLink, Check } from "lucide-react"

export default function MobileScreen5_2() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Toast Notification */}
      <div className="absolute top-20 left-4 right-4 z-50">
        <div className="bg-blue-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center gap-3 animate-in slide-in-from-top duration-300">
          <ExternalLink className="w-5 h-5" />
          <div className="flex-1">
            <p className="text-sm font-medium">Opening TechSound Store</p>
            <p className="text-xs opacity-90">You'll be redirected in a new tab</p>
          </div>
          <Check className="w-5 h-5" />
        </div>
      </div>

      {/* Main Content (Background) */}
      <div className="flex-1 p-4">
        <div className="bg-gray-100 rounded-xl h-full flex items-center justify-center">
          <p className="text-gray-500">Background content...</p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
