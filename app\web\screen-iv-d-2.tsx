"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Save, Database, Server, Shield, Globe } from "lucide-react"
import Link from "next/link"

export default function WebScreenIVD2() {
  const [databaseConfig, setDatabaseConfig] = useState({
    host: "localhost",
    port: "5432",
    database: "giftspark_prod",
    username: "admin",
    password: "",
    maxConnections: "100",
    connectionTimeout: "30",
    ssl: true,
  })

  const [serverConfig, setServerConfig] = useState({
    environment: "production",
    port: "3000",
    nodeEnv: "production",
    logLevel: "info",
    maxMemory: "2048",
    workers: "4",
  })

  const [securityConfig, setSecurityConfig] = useState({
    jwtSecret: "",
    sessionTimeout: "24",
    rateLimitRequests: "100",
    rateLimitWindow: "15",
    corsOrigins: "https://giftspark.com",
    enableHttps: true,
    enableHsts: true,
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href="/web/screen-iv-d-1">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to System
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">System Configuration</h1>
              <p className="text-gray-600">Configure system settings and environment</p>
            </div>
          </div>
          <Button>
            <Save className="h-4 w-4 mr-2" />
            Save Configuration
          </Button>
        </div>
      </div>

      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          <Tabs defaultValue="database" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="database" className="flex items-center gap-2">
                <Database className="h-4 w-4" />
                Database
              </TabsTrigger>
              <TabsTrigger value="server" className="flex items-center gap-2">
                <Server className="h-4 w-4" />
                Server
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                API
              </TabsTrigger>
            </TabsList>

            <TabsContent value="database">
              <Card>
                <CardHeader>
                  <CardTitle>Database Configuration</CardTitle>
                  <CardDescription>Configure database connection and settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="db-host">Host</Label>
                      <Input
                        id="db-host"
                        value={databaseConfig.host}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, host: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="db-port">Port</Label>
                      <Input
                        id="db-port"
                        value={databaseConfig.port}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, port: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="db-name">Database Name</Label>
                      <Input
                        id="db-name"
                        value={databaseConfig.database}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, database: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="db-username">Username</Label>
                      <Input
                        id="db-username"
                        value={databaseConfig.username}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, username: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="db-password">Password</Label>
                      <Input
                        id="db-password"
                        type="password"
                        value={databaseConfig.password}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, password: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="max-connections">Max Connections</Label>
                      <Input
                        id="max-connections"
                        type="number"
                        value={databaseConfig.maxConnections}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, maxConnections: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="connection-timeout">Connection Timeout (seconds)</Label>
                      <Input
                        id="connection-timeout"
                        type="number"
                        value={databaseConfig.connectionTimeout}
                        onChange={(e) => setDatabaseConfig({ ...databaseConfig, connectionTimeout: e.target.value })}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="ssl-enabled"
                        checked={databaseConfig.ssl}
                        onCheckedChange={(checked) => setDatabaseConfig({ ...databaseConfig, ssl: checked })}
                      />
                      <Label htmlFor="ssl-enabled">Enable SSL</Label>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button variant="outline">Test Connection</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="server">
              <Card>
                <CardHeader>
                  <CardTitle>Server Configuration</CardTitle>
                  <CardDescription>Configure server environment and performance settings</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="environment">Environment</Label>
                      <Select
                        value={serverConfig.environment}
                        onValueChange={(value) => setServerConfig({ ...serverConfig, environment: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="development">Development</SelectItem>
                          <SelectItem value="staging">Staging</SelectItem>
                          <SelectItem value="production">Production</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="server-port">Port</Label>
                      <Input
                        id="server-port"
                        value={serverConfig.port}
                        onChange={(e) => setServerConfig({ ...serverConfig, port: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="node-env">Node Environment</Label>
                      <Select
                        value={serverConfig.nodeEnv}
                        onValueChange={(value) => setServerConfig({ ...serverConfig, nodeEnv: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="development">Development</SelectItem>
                          <SelectItem value="production">Production</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="log-level">Log Level</Label>
                      <Select
                        value={serverConfig.logLevel}
                        onValueChange={(value) => setServerConfig({ ...serverConfig, logLevel: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="error">Error</SelectItem>
                          <SelectItem value="warn">Warning</SelectItem>
                          <SelectItem value="info">Info</SelectItem>
                          <SelectItem value="debug">Debug</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="max-memory">Max Memory (MB)</Label>
                      <Input
                        id="max-memory"
                        type="number"
                        value={serverConfig.maxMemory}
                        onChange={(e) => setServerConfig({ ...serverConfig, maxMemory: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="workers">Worker Processes</Label>
                      <Input
                        id="workers"
                        type="number"
                        value={serverConfig.workers}
                        onChange={(e) => setServerConfig({ ...serverConfig, workers: e.target.value })}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>Security Configuration</CardTitle>
                  <CardDescription>Configure security settings and authentication</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="jwt-secret">JWT Secret</Label>
                      <Input
                        id="jwt-secret"
                        type="password"
                        value={securityConfig.jwtSecret}
                        onChange={(e) => setSecurityConfig({ ...securityConfig, jwtSecret: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="session-timeout">Session Timeout (hours)</Label>
                      <Input
                        id="session-timeout"
                        type="number"
                        value={securityConfig.sessionTimeout}
                        onChange={(e) => setSecurityConfig({ ...securityConfig, sessionTimeout: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="rate-limit-requests">Rate Limit (requests)</Label>
                      <Input
                        id="rate-limit-requests"
                        type="number"
                        value={securityConfig.rateLimitRequests}
                        onChange={(e) => setSecurityConfig({ ...securityConfig, rateLimitRequests: e.target.value })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="rate-limit-window">Rate Limit Window (minutes)</Label>
                      <Input
                        id="rate-limit-window"
                        type="number"
                        value={securityConfig.rateLimitWindow}
                        onChange={(e) => setSecurityConfig({ ...securityConfig, rateLimitWindow: e.target.value })}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <Label htmlFor="cors-origins">CORS Origins</Label>
                      <Textarea
                        id="cors-origins"
                        placeholder="https://example.com, https://app.example.com"
                        value={securityConfig.corsOrigins}
                        onChange={(e) => setSecurityConfig({ ...securityConfig, corsOrigins: e.target.value })}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enable-https"
                        checked={securityConfig.enableHttps}
                        onCheckedChange={(checked) => setSecurityConfig({ ...securityConfig, enableHttps: checked })}
                      />
                      <Label htmlFor="enable-https">Enable HTTPS</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enable-hsts"
                        checked={securityConfig.enableHsts}
                        onCheckedChange={(checked) => setSecurityConfig({ ...securityConfig, enableHsts: checked })}
                      />
                      <Label htmlFor="enable-hsts">Enable HSTS</Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api">
              <Card>
                <CardHeader>
                  <CardTitle>API Configuration</CardTitle>
                  <CardDescription>Configure API settings and external integrations</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div>
                      <Label>API Version</Label>
                      <Select defaultValue="v1">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="v1">Version 1.0</SelectItem>
                          <SelectItem value="v2">Version 2.0 (Beta)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>API Documentation URL</Label>
                      <Input value="https://api.giftspark.com/docs" readOnly />
                    </div>
                    <div>
                      <Label>Webhook Endpoints</Label>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">POST</Badge>
                          <span className="text-sm">/webhooks/payment</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">POST</Badge>
                          <span className="text-sm">/webhooks/user-events</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
