import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Calendar, Gift, Bell, Trash2, Edit } from "lucide-react"

export default function MobileScreen9_3() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Date Details</h1>
        <Edit className="w-6 h-6 text-gray-600" />
      </div>

      {/* Date Header */}
      <div className="px-6 py-6 bg-gradient-to-r from-red-50 to-orange-50 border-b border-orange-100">
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center">
            <Calendar className="w-8 h-8 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Mom's Birthday</h2>
            <p className="text-gray-600">June 15, 2025 • Family</p>
            <div className="mt-1 px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full inline-block">
              12 days left
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-6 space-y-6">
        {/* Person Details */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
          <h3 className="font-medium text-gray-900 mb-3">Person Details</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Name</span>
              <span className="font-medium text-gray-900">Lin Mei</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Relationship</span>
              <span className="font-medium text-gray-900">Mother</span>
            </div>
          </div>
        </div>

        {/* Date Settings */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
          <h3 className="font-medium text-gray-900 mb-3">Date Settings</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Repeats</span>
              <span className="font-medium text-gray-900">Yearly</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Reminder</span>
              <span className="font-medium text-gray-900">1 week before</span>
            </div>
          </div>
        </div>

        {/* Gift Ideas */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Gift Ideas</h3>
            <Button variant="ghost" size="sm" className="h-8 text-orange-600">
              Add Idea
            </Button>
          </div>
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Gift className="w-5 h-5 text-orange-500" />
              <span className="text-gray-900">Silk scarf from her favorite brand</span>
            </div>
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
              <Gift className="w-5 h-5 text-orange-500" />
              <span className="text-gray-900">Tea set with premium Chinese tea</span>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div className="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
          <h3 className="font-medium text-gray-900 mb-3">Notes</h3>
          <p className="text-gray-600">
            Mom loves traditional Chinese tea and has been talking about getting a new tea set. She also mentioned
            wanting a new silk scarf for special occasions.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="space-y-3">
          <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
            <Gift className="w-5 h-5 mr-2" />
            Find Gift Recommendations
          </Button>
          <Button variant="outline" className="w-full">
            <Bell className="w-5 h-5 mr-2" />
            Adjust Reminder
          </Button>
        </div>
      </div>

      {/* Delete Button */}
      <div className="absolute bottom-8 left-0 right-0 px-6">
        <Button variant="ghost" className="w-full text-red-600">
          <Trash2 className="w-5 h-5 mr-2" />
          Delete Date
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
