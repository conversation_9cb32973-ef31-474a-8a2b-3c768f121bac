import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Search, Mail, Gift, Heart, Star, Cake, Coffee } from "lucide-react"

export default function MobileScreen10_1() {
  const cardCategories = [
    { name: "Birthday", icon: Cake, count: 24 },
    { name: "Thank You", icon: Heart, count: 18 },
    { name: "Congratulations", icon: Star, count: 15 },
    { name: "Holiday", icon: Gift, count: 22 },
    { name: "Just Because", icon: Coffee, count: 12 },
  ]

  const recentCards = [
    {
      id: 1,
      name: "Birthday Wishes",
      image: "/placeholder.svg?height=120&width=120",
      category: "Birthday",
    },
    {
      id: 2,
      name: "Thank You Note",
      image: "/placeholder.svg?height=120&width=120",
      category: "Thank You",
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">E-Cards</h1>
        <div className="w-6"></div>
      </div>

      {/* Search Bar */}
      <div className="px-4 py-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input placeholder="Search e-cards..." className="pl-10 h-12 bg-gray-50 border-0" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 pb-24 overflow-y-auto">
        {/* Card Categories */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Card Categories</h2>
          <div className="grid grid-cols-2 gap-3">
            {cardCategories.map((category, index) => {
              const IconComponent = category.icon
              return (
                <div
                  key={index}
                  className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl border border-gray-100 hover:bg-gray-100 transition-colors"
                >
                  <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                    <IconComponent className="w-5 h-5 text-orange-500" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{category.name}</p>
                    <p className="text-xs text-gray-600">{category.count} cards</p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Recently Used */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recently Used</h2>
          <div className="grid grid-cols-2 gap-4">
            {recentCards.map((card) => (
              <div key={card.id} className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                <img
                  src={card.image || "/placeholder.svg"}
                  alt={card.name}
                  className="w-full h-32 object-cover bg-gray-100"
                />
                <div className="p-3">
                  <h3 className="font-medium text-gray-900 text-sm">{card.name}</h3>
                  <p className="text-xs text-gray-600">{card.category}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Cards */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Featured Cards</h2>
          <div className="grid grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((id) => (
              <div key={id} className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                <img
                  src={`/placeholder.svg?height=120&width=120&text=Card+${id}`}
                  alt={`Featured Card ${id}`}
                  className="w-full h-32 object-cover bg-gray-100"
                />
                <div className="p-3">
                  <h3 className="font-medium text-gray-900 text-sm">Featured Design {id}</h3>
                  <p className="text-xs text-gray-600">
                    {id % 2 === 0 ? "Birthday" : id % 3 === 0 ? "Thank You" : "Congratulations"}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4">
        <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
          <Mail className="w-5 h-5 mr-2" />
          Create New E-Card
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
