import { Button } from "@/components/ui/button"
import { LogOut } from "lucide-react"

export default function MobileScreen8_14() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Logout Confirmation Modal */}
      <div className="absolute inset-0 flex items-center justify-center z-20 p-6">
        <div className="bg-white rounded-2xl p-6 w-full max-w-sm shadow-2xl">
          {/* Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <LogOut className="w-8 h-8 text-red-600" />
            </div>
          </div>

          {/* Title */}
          <h2 className="text-xl font-bold text-center text-gray-900 mb-4">Log Out</h2>

          {/* Description */}
          <p className="text-gray-600 text-center mb-6 leading-relaxed">
            Are you sure you want to log out of your GiftSpark AI account? You'll need to sign in again to access your
            saved gifts and preferences.
          </p>

          {/* Action Buttons */}
          <div className="space-y-3">
            <Button className="w-full bg-red-600 hover:bg-red-700 text-white">
              <LogOut className="w-5 h-5 mr-2" />
              Yes, Log Out
            </Button>
            <Button variant="outline" className="w-full">
              Cancel
            </Button>
          </div>
        </div>
      </div>

      {/* Alternative: Account Deletion Modal */}
      {/* Uncomment this section to show account deletion instead */}
      {/*
      <div className="absolute inset-0 flex items-center justify-center z-20 p-6">
        <div className="bg-white rounded-2xl p-6 w-full max-w-sm shadow-2xl">
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </div>

          <h2 className="text-xl font-bold text-center text-gray-900 mb-4">Delete Account</h2>

          <p className="text-gray-600 text-center mb-6 leading-relaxed">
            This action cannot be undone. All your data, including saved gifts, date reminders, and preferences will be permanently deleted.
          </p>

          <div className="bg-red-50 rounded-lg p-4 mb-6 border border-red-200">
            <h4 className="font-medium text-red-900 mb-2">This will delete:</h4>
            <ul className="text-sm text-red-800 space-y-1">
              <li>• Your profile and account data</li>
              <li>• Saved gifts and wishlist</li>
              <li>• Date reminders and preferences</li>
              <li>• E-card history</li>
            </ul>
          </div>

          <div className="space-y-3">
            <Button className="w-full bg-red-600 hover:bg-red-700 text-white">
              <Trash2 className="w-5 h-5 mr-2" />
              Delete My Account
            </Button>
            <Button variant="outline" className="w-full">
              Cancel
            </Button>
          </div>
        </div>
      </div>
      */}

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
