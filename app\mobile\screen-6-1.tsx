"use client"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Search, Heart, Share2, Filter, Sparkles, Gift, Calendar, Users, ArrowRight } from "lucide-react"

export default function MobileScreen6_1() {
  return (
    <div className="max-w-sm mx-auto bg-white min-h-screen">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold">Discover</h1>
          <Button variant="ghost" size="sm" className="text-white hover:bg-white/20">
            <Filter className="w-4 h-4" />
          </Button>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search gift ideas..."
            className="pl-10 bg-white/20 border-white/30 text-white placeholder:text-white/70"
          />
        </div>
      </div>

      {/* Quick Categories */}
      <div className="p-4">
        <div className="flex gap-2 overflow-x-auto pb-2">
          <Badge variant="secondary" className="whitespace-nowrap">
            <Sparkles className="w-3 h-3 mr-1" />
            Trending
          </Badge>
          <Badge variant="outline" className="whitespace-nowrap">
            <Gift className="w-3 h-3 mr-1" />
            For Her
          </Badge>
          <Badge variant="outline" className="whitespace-nowrap">
            <Users className="w-3 h-3 mr-1" />
            For Him
          </Badge>
          <Badge variant="outline" className="whitespace-nowrap">
            <Calendar className="w-3 h-3 mr-1" />
            Occasions
          </Badge>
        </div>
      </div>

      {/* Featured Collections */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">Featured Collections</h2>
          <Button variant="ghost" size="sm">
            View All
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>

        <div className="space-y-3">
          {/* Collection Card 1 */}
          <Card className="overflow-hidden">
            <div className="relative">
              <div className="h-32 bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center">
                <Gift className="w-12 h-12 text-purple-600" />
              </div>
              <div className="absolute top-2 right-2">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white/80 hover:bg-white">
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <CardContent className="p-3">
              <h3 className="font-semibold mb-1">Valentine's Day Specials</h3>
              <p className="text-sm text-gray-600 mb-2">Romantic gifts for your loved one</p>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  24 items
                </Badge>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Collection Card 2 */}
          <Card className="overflow-hidden">
            <div className="relative">
              <div className="h-32 bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
                <Sparkles className="w-12 h-12 text-blue-600" />
              </div>
              <div className="absolute top-2 right-2">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white/80 hover:bg-white">
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <CardContent className="p-3">
              <h3 className="font-semibold mb-1">Tech Enthusiast</h3>
              <p className="text-sm text-gray-600 mb-2">Latest gadgets and innovations</p>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  18 items
                </Badge>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Collection Card 3 */}
          <Card className="overflow-hidden">
            <div className="relative">
              <div className="h-32 bg-gradient-to-br from-yellow-100 to-orange-100 flex items-center justify-center">
                <Users className="w-12 h-12 text-orange-600" />
              </div>
              <div className="absolute top-2 right-2">
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 bg-white/80 hover:bg-white">
                  <Heart className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <CardContent className="p-3">
              <h3 className="font-semibold mb-1">Family Favorites</h3>
              <p className="text-sm text-gray-600 mb-2">Perfect for family gatherings</p>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  32 items
                </Badge>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Trending Now */}
      <div className="px-4 pb-6">
        <h2 className="text-lg font-semibold mb-3">Trending Now</h2>
        <div className="grid grid-cols-2 gap-3">
          <Card className="overflow-hidden">
            <div className="h-24 bg-gradient-to-br from-red-100 to-pink-100 flex items-center justify-center">
              <Gift className="w-8 h-8 text-red-600" />
            </div>
            <CardContent className="p-2">
              <p className="text-sm font-medium">Personalized Jewelry</p>
              <p className="text-xs text-gray-600">$25-$150</p>
            </CardContent>
          </Card>

          <Card className="overflow-hidden">
            <div className="h-24 bg-gradient-to-br from-green-100 to-teal-100 flex items-center justify-center">
              <Sparkles className="w-8 h-8 text-green-600" />
            </div>
            <CardContent className="p-2">
              <p className="text-sm font-medium">Eco-Friendly Sets</p>
              <p className="text-xs text-gray-600">$15-$80</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
