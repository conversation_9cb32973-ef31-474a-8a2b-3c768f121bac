"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Download,
  Upload,
  Database,
  Calendar,
  Clock,
  AlertCircle,
  Play,
  Pause,
  RotateCcw,
  HardDrive,
  Cloud,
} from "lucide-react"

export default function BackupRestoreScreen() {
  const [backupInProgress, setBackupInProgress] = useState(false)
  const [restoreInProgress, setRestoreInProgress] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)

  const backupHistory = [
    {
      id: 1,
      name: "Full System Backup",
      date: "2024-01-15 02:00:00",
      size: "2.4 GB",
      type: "Automatic",
      status: "completed",
      location: "AWS S3",
    },
    {
      id: 2,
      name: "Database Backup",
      date: "2024-01-14 14:30:00",
      size: "856 MB",
      type: "Manual",
      status: "completed",
      location: "Local Storage",
    },
    {
      id: 3,
      name: "User Data Backup",
      date: "2024-01-14 02:00:00",
      size: "1.2 GB",
      type: "Automatic",
      status: "completed",
      location: "AWS S3",
    },
    {
      id: 4,
      name: "Configuration Backup",
      date: "2024-01-13 18:45:00",
      size: "45 MB",
      type: "Manual",
      status: "failed",
      location: "Local Storage",
    },
    {
      id: 5,
      name: "Full System Backup",
      date: "2024-01-13 02:00:00",
      size: "2.3 GB",
      type: "Automatic",
      status: "completed",
      location: "AWS S3",
    },
  ]

  const schedules = [
    {
      id: 1,
      name: "Daily Database Backup",
      frequency: "Daily",
      time: "02:00",
      enabled: true,
      nextRun: "2024-01-16 02:00:00",
    },
    {
      id: 2,
      name: "Weekly Full Backup",
      frequency: "Weekly",
      time: "01:00",
      enabled: true,
      nextRun: "2024-01-21 01:00:00",
    },
    {
      id: 3,
      name: "Monthly Archive",
      frequency: "Monthly",
      time: "00:00",
      enabled: false,
      nextRun: "2024-02-01 00:00:00",
    },
  ]

  const handleStartBackup = () => {
    setBackupInProgress(true)
    setBackupProgress(0)

    // Simulate backup progress
    const interval = setInterval(() => {
      setBackupProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setBackupInProgress(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Backup & Restore</h1>
        <p className="text-gray-600">Manage system backups and data recovery</p>
      </div>

      <Tabs defaultValue="backup" className="space-y-6">
        <TabsList>
          <TabsTrigger value="backup">Create Backup</TabsTrigger>
          <TabsTrigger value="restore">Restore Data</TabsTrigger>
          <TabsTrigger value="history">Backup History</TabsTrigger>
          <TabsTrigger value="schedule">Scheduled Backups</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Create Backup Tab */}
        <TabsContent value="backup">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="h-5 w-5" />
                  Create New Backup
                </CardTitle>
                <CardDescription>Select backup type and options</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="backup-name">Backup Name</Label>
                  <Input
                    id="backup-name"
                    placeholder="Enter backup name"
                    defaultValue={`Backup_${new Date().toISOString().split("T")[0]}`}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Backup Type</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" className="justify-start">
                      <Database className="h-4 w-4 mr-2" />
                      Full System
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <Database className="h-4 w-4 mr-2" />
                      Database Only
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <HardDrive className="h-4 w-4 mr-2" />
                      User Data
                    </Button>
                    <Button variant="outline" className="justify-start">
                      <HardDrive className="h-4 w-4 mr-2" />
                      Configuration
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Storage Location</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="storage" value="local" defaultChecked />
                      <HardDrive className="h-4 w-4" />
                      <span>Local Storage</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="storage" value="cloud" />
                      <Cloud className="h-4 w-4" />
                      <span>Cloud Storage (AWS S3)</span>
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="backup-description">Description (Optional)</Label>
                  <Textarea id="backup-description" placeholder="Add backup description" rows={3} />
                </div>

                <Button className="w-full" onClick={handleStartBackup} disabled={backupInProgress}>
                  {backupInProgress ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Creating Backup...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Start Backup
                    </>
                  )}
                </Button>

                {backupInProgress && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{backupProgress}%</span>
                    </div>
                    <Progress value={backupProgress} />
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Backup Information</CardTitle>
                <CardDescription>Current system status and backup requirements</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Database Size</span>
                    <Badge variant="outline">856 MB</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">User Files</span>
                    <Badge variant="outline">1.2 GB</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">System Config</span>
                    <Badge variant="outline">45 MB</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Application Data</span>
                    <Badge variant="outline">324 MB</Badge>
                  </div>
                  <hr />
                  <div className="flex justify-between items-center font-medium">
                    <span>Estimated Total</span>
                    <Badge>2.4 GB</Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Storage Status</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Local Storage Available</span>
                      <span>45.2 GB</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Cloud Storage Available</span>
                      <span>Unlimited</span>
                    </div>
                  </div>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-4 w-4 text-blue-500 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-900">Backup Recommendation</p>
                      <p className="text-blue-700">
                        Last full backup was 1 day ago. Regular backups are recommended for data safety.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Restore Data Tab */}
        <TabsContent value="restore">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Restore from Backup
              </CardTitle>
              <CardDescription>Select a backup to restore system data</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Select Backup</Label>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {backupHistory
                        .filter((backup) => backup.status === "completed")
                        .map((backup) => (
                          <label
                            key={backup.id}
                            className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                          >
                            <input type="radio" name="restore-backup" value={backup.id} />
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <p className="font-medium">{backup.name}</p>
                                <Badge variant="outline">{backup.size}</Badge>
                              </div>
                              <p className="text-sm text-gray-500">{backup.date}</p>
                              <p className="text-xs text-gray-400">{backup.location}</p>
                            </div>
                          </label>
                        ))}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Restore Options</Label>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked />
                        <span className="text-sm">Restore Database</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked />
                        <span className="text-sm">Restore User Files</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" />
                        <span className="text-sm">Restore System Configuration</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" />
                        <span className="text-sm">Overwrite Existing Data</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-900">Important Notice</h4>
                        <ul className="text-sm text-yellow-800 mt-2 space-y-1">
                          <li>• Restore process will temporarily stop the system</li>
                          <li>• Current data may be overwritten</li>
                          <li>• Create a backup before restoring if needed</li>
                          <li>• Estimated restore time: 15-30 minutes</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="restore-confirmation">Confirmation</Label>
                    <Input id="restore-confirmation" placeholder="Type 'RESTORE' to confirm" />
                  </div>

                  <Button variant="destructive" className="w-full" disabled={restoreInProgress}>
                    {restoreInProgress ? (
                      <>
                        <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                        Restoring...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4 mr-2" />
                        Start Restore Process
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Backup History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Backup History</CardTitle>
              <CardDescription>View and manage previous backups</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {backupHistory.map((backup) => (
                  <div key={backup.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          backup.status === "completed"
                            ? "bg-green-500"
                            : backup.status === "failed"
                              ? "bg-red-500"
                              : "bg-yellow-500"
                        }`}
                      />
                      <div>
                        <h4 className="font-medium">{backup.name}</h4>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {backup.date}
                          </span>
                          <span>{backup.size}</span>
                          <Badge variant={backup.type === "Automatic" ? "default" : "secondary"} className="text-xs">
                            {backup.type}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          backup.status === "completed"
                            ? "default"
                            : backup.status === "failed"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {backup.status}
                      </Badge>
                      {backup.status === "completed" && (
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm">
                            Download
                          </Button>
                          <Button variant="outline" size="sm">
                            Restore
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            Delete
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Backups Tab */}
        <TabsContent value="schedule">
          <Card>
            <CardHeader>
              <CardTitle>Scheduled Backups</CardTitle>
              <CardDescription>Manage automatic backup schedules</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {schedules.map((schedule) => (
                <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{schedule.name}</h4>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {schedule.frequency} at {schedule.time}
                      </span>
                      <span>Next run: {schedule.nextRun}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <Badge variant={schedule.enabled ? "default" : "secondary"}>
                      {schedule.enabled ? "Enabled" : "Disabled"}
                    </Badge>
                    <div className="flex gap-1">
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        {schedule.enabled ? "Disable" : "Enable"}
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600">
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              ))}

              <Button className="w-full" variant="outline">
                <Play className="h-4 w-4 mr-2" />
                Add New Schedule
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Backup Settings</CardTitle>
                <CardDescription>Configure backup preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="retention-days">Backup Retention (days)</Label>
                  <Input id="retention-days" type="number" defaultValue="30" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="compression">Compression Level</Label>
                  <select className="w-full px-3 py-2 border rounded-md">
                    <option value="none">No Compression</option>
                    <option value="low">Low</option>
                    <option value="medium" selected>
                      Medium
                    </option>
                    <option value="high">High</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="max-backups">Max Concurrent Backups</Label>
                  <Input id="max-backups" type="number" defaultValue="2" />
                </div>
                <div className="space-y-2">
                  <Label>Email Notifications</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Backup completion</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Backup failures</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" />
                      <span className="text-sm">Weekly backup summary</span>
                    </label>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Storage Configuration</CardTitle>
                <CardDescription>Configure backup storage locations</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="local-path">Local Storage Path</Label>
                  <Input id="local-path" defaultValue="/var/backups/giftspark" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="s3-bucket">AWS S3 Bucket</Label>
                  <Input id="s3-bucket" defaultValue="giftspark-backups" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="s3-region">AWS Region</Label>
                  <Input id="s3-region" defaultValue="us-east-1" />
                </div>
                <div className="space-y-2">
                  <Label>Storage Usage</Label>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Local Storage</span>
                      <span>12.5 GB / 100 GB</span>
                    </div>
                    <Progress value={12.5} />
                    <div className="flex justify-between text-sm">
                      <span>Cloud Storage</span>
                      <span>45.8 GB / Unlimited</span>
                    </div>
                  </div>
                </div>
                <Button variant="outline" className="w-full">
                  Test Connection
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
