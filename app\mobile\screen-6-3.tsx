"use client"

import { Input } from "@/components/ui/input"
import { ArrowLeft, X, Sparkles, Compass, Calendar, User } from "lucide-react"
import { useState } from "react"

export default function MobileScreen6_3() {
  const [searchQuery, setSearchQuery] = useState("")

  const trendingSearches = ["holiday gifts", "tech gadgets", "home decor", "books", "wellness"]

  const categories = [
    { name: "Electronics", icon: "📱" },
    { name: "Fashion", icon: "👗" },
    { name: "Home & Garden", icon: "🏠" },
    { name: "Books", icon: "📚" },
    { name: "Sports", icon: "⚽" },
    { name: "Beauty", icon: "💄" },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Search Header */}
      <div className="flex items-center gap-3 p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <div className="flex-1 relative">
          <Input
            type="text"
            placeholder="Search collections, themes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-10 pr-10 bg-gray-50 border-0 focus:bg-white focus:ring-2 focus:ring-orange-500"
            autoFocus
          />
          {searchQuery && (
            <button onClick={() => setSearchQuery("")} className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <X className="w-4 h-4 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Search Content */}
      <div className="flex-1 px-4 py-4">
        {/* Trending Searches */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Trending in Discover</h3>
          <div className="space-y-2">
            {trendingSearches.map((search, index) => (
              <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-xs">🔥</span>
                </div>
                <span className="text-gray-700">{search}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Browse by Category</h3>
          <div className="grid grid-cols-2 gap-3">
            {categories.map((category, index) => (
              <div
                key={index}
                className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
              >
                <span className="text-2xl">{category.icon}</span>
                <span className="font-medium text-gray-900">{category.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="absolute bottom-8 left-0 right-0 bg-white/90 backdrop-blur-sm mx-4 rounded-2xl border border-white/20">
        <div className="flex items-center justify-around py-3">
          <div className="flex flex-col items-center gap-1">
            <Sparkles className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">Recommend</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <Compass className="w-4 h-4 text-white" />
            </div>
            <span className="text-xs font-medium text-orange-500">Discover</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <Calendar className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">My Dates</span>
          </div>
          <div className="flex flex-col items-center gap-1">
            <User className="w-6 h-6 text-gray-400" />
            <span className="text-xs text-gray-400">Profile</span>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
