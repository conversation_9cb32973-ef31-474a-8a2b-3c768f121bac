"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Lock, Eye, EyeOff, CheckCircle } from "lucide-react"
import { useState } from "react"

export default function MobileScreen8_7() {
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Create New Password</h1>
        <div className="w-6"></div>
      </div>

      {/* Content */}
      <div className="flex-1 px-6 py-8">
        {/* Icon */}
        <div className="flex items-center justify-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <Lock className="w-8 h-8 text-green-600" />
          </div>
        </div>

        {/* Title */}
        <h1 className="text-2xl font-bold text-center text-gray-900 mb-4">Create New Password</h1>
        <p className="text-gray-600 text-center mb-8 leading-relaxed">
          Your new password must be different from your previous password and meet our security requirements.
        </p>

        {/* Form */}
        <div className="space-y-6 mb-8">
          {/* New Password */}
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type={showNewPassword ? "text" : "password"}
              placeholder="New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="pl-12 pr-12 h-12 bg-gray-50 border-0"
            />
            <button
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
            >
              {showNewPassword ? (
                <EyeOff className="w-5 h-5 text-gray-400" />
              ) : (
                <Eye className="w-5 h-5 text-gray-400" />
              )}
            </button>
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="pl-12 pr-12 h-12 bg-gray-50 border-0"
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2"
            >
              {showConfirmPassword ? (
                <EyeOff className="w-5 h-5 text-gray-400" />
              ) : (
                <Eye className="w-5 h-5 text-gray-400" />
              )}
            </button>
          </div>
        </div>

        {/* Password Requirements */}
        <div className="bg-gray-50 rounded-xl p-4 mb-8">
          <h3 className="font-medium text-gray-900 mb-3">Password Requirements</h3>
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <span className="text-sm text-gray-700">At least 8 characters</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
              <span className="text-sm text-gray-500">One uppercase letter</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
              <span className="text-sm text-gray-500">One number</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
              <span className="text-sm text-gray-500">One special character</span>
            </div>
          </div>
        </div>

        {/* Reset Password Button */}
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white font-semibold">
          Reset Password
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
