"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, ChevronRight, Star, Sparkles } from "lucide-react"
import { IPhoneFrame } from "@/components/iphone-frame"

const occasions = [
  { id: "birthday", name: "Birthday", icon: "🎂", color: "bg-pink-100 text-pink-700", popular: true },
  { id: "anniversary", name: "Anniversary", icon: "💕", color: "bg-red-100 text-red-700", popular: true },
  { id: "wedding", name: "Wedding", icon: "💒", color: "bg-purple-100 text-purple-700", popular: false },
  { id: "graduation", name: "Graduation", icon: "🎓", color: "bg-blue-100 text-blue-700", popular: true },
  { id: "christmas", name: "Christmas", icon: "🎄", color: "bg-green-100 text-green-700", popular: true },
  { id: "valentines", name: "Valentine's Day", icon: "💝", color: "bg-rose-100 text-rose-700", popular: true },
  { id: "mothers-day", name: "Mother's Day", icon: "🌸", color: "bg-pink-100 text-pink-700", popular: false },
  { id: "fathers-day", name: "Father's Day", icon: "👔", color: "bg-blue-100 text-blue-700", popular: false },
  { id: "baby-shower", name: "Baby Shower", icon: "👶", color: "bg-yellow-100 text-yellow-700", popular: false },
  { id: "housewarming", name: "Housewarming", icon: "🏠", color: "bg-orange-100 text-orange-700", popular: false },
  { id: "thank-you", name: "Thank You", icon: "🙏", color: "bg-indigo-100 text-indigo-700", popular: false },
  { id: "just-because", name: "Just Because", icon: "✨", color: "bg-purple-100 text-purple-700", popular: false },
]

export default function MobileScreen1_2() {
  const [selectedOccasion, setSelectedOccasion] = useState<string | null>(null)

  const popularOccasions = occasions.filter((o) => o.popular)
  const otherOccasions = occasions.filter((o) => !o.popular)

  return (
    <IPhoneFrame>
      <div className="flex flex-col h-full bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
        {/* Header */}
        <div className="px-6 pt-6 pb-4">
          <div className="flex items-center justify-between mb-6">
            <Button variant="ghost" size="sm" className="rounded-full p-2">
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div className="text-center">
              <h1 className="text-lg font-semibold text-gray-900">Choose Occasion</h1>
              <p className="text-sm text-gray-600">Step 1 of 4</p>
            </div>
            <div className="w-9"></div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 h-2 rounded-full w-1/4"></div>
          </div>

          {/* Question */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-sm">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-lg font-semibold text-gray-900">What's the occasion?</h2>
            </div>
            <p className="text-gray-600 text-sm">Help us understand the context so we can suggest the perfect gifts</p>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 px-6 space-y-6 overflow-y-auto">
          {/* Popular Occasions */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Star className="w-4 h-4 text-yellow-500" />
              <h3 className="text-lg font-semibold text-gray-900">Popular</h3>
            </div>
            <div className="grid grid-cols-2 gap-3">
              {popularOccasions.map((occasion) => (
                <Card
                  key={occasion.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedOccasion === occasion.id
                      ? "ring-2 ring-purple-500 bg-purple-50 border-purple-200"
                      : "bg-white/80 backdrop-blur-sm border-white/20 hover:bg-white"
                  }`}
                  onClick={() => setSelectedOccasion(occasion.id)}
                >
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl mb-2">{occasion.icon}</div>
                      <h4 className="font-medium text-gray-900 text-sm mb-1">{occasion.name}</h4>
                      <Badge variant="secondary" className={`text-xs ${occasion.color}`}>
                        Popular
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Other Occasions */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Other Occasions</h3>
            <div className="space-y-3">
              {otherOccasions.map((occasion) => (
                <Card
                  key={occasion.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedOccasion === occasion.id
                      ? "ring-2 ring-purple-500 bg-purple-50 border-purple-200"
                      : "bg-white/80 backdrop-blur-sm border-white/20 hover:bg-white"
                  }`}
                  onClick={() => setSelectedOccasion(occasion.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="text-xl">{occasion.icon}</div>
                        <h4 className="font-medium text-gray-900">{occasion.name}</h4>
                      </div>
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Action */}
        <div className="px-6 py-4 bg-white/90 backdrop-blur-sm border-t border-white/20">
          <Button
            className={`w-full ${
              selectedOccasion
                ? "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                : "bg-gray-300"
            }`}
            disabled={!selectedOccasion}
          >
            Continue
            <ChevronRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </IPhoneFrame>
  )
}
