"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft } from "lucide-react"
import { useState } from "react"

export default function MobileScreen1_8() {
  const [selectedAge, setSelectedAge] = useState("")
  const [selectedGender, setSelectedGender] = useState("")
  const [interests, setInterests] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const suggestedTags = [
    "Tech",
    "Fashion",
    "Books",
    "Travel",
    "DIY",
    "Cooking",
    "Sports",
    "Music",
    "Art",
    "Gaming",
    "Fitness",
    "Photography",
    "Gardening",
  ]

  const toggleTag = (tag: string) => {
    setSelectedTags((prev) => (prev.includes(tag) ? prev.filter((t) => t !== tag) : [...prev, tag]))
  }

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Tell us about the recipient</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Indicator */}
      <div className="px-4 py-3">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 4 of 4</p>
      </div>

      {/* Instruction */}
      <div className="px-4 py-3 bg-blue-50 border-b border-blue-100">
        <p className="text-sm text-blue-800">
          <span className="font-medium">All fields are optional</span>, but more info helps AI find better gifts!
        </p>
      </div>

      {/* Form Content */}
      <div className="flex-1 px-4 py-4 pb-32 space-y-6 overflow-y-auto">
        {/* Age Range */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">
            Age Range <span className="text-gray-400">(Optional)</span>
          </label>
          <Select value={selectedAge} onValueChange={setSelectedAge}>
            <SelectTrigger className="h-12">
              <SelectValue placeholder="Select age range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="child">Child (0-12)</SelectItem>
              <SelectItem value="teen">Teen (13-17)</SelectItem>
              <SelectItem value="young-adult">18-25</SelectItem>
              <SelectItem value="adult">26-35</SelectItem>
              <SelectItem value="middle-age">35-45</SelectItem>
              <SelectItem value="mature">45+</SelectItem>
              <SelectItem value="prefer-not-say">Prefer not to say</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Gender */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-3 block">
            Gender <span className="text-gray-400">(Optional)</span>
          </label>
          <div className="grid grid-cols-2 gap-3">
            {["Male", "Female", "Other", "Prefer not to say"].map((gender) => (
              <Button
                key={gender}
                variant={selectedGender === gender ? "default" : "outline"}
                className={`h-12 ${
                  selectedGender === gender
                    ? "bg-orange-500 hover:bg-orange-600 text-white"
                    : "border-gray-200 text-gray-700 hover:bg-gray-50"
                }`}
                onClick={() => setSelectedGender(gender)}
              >
                {gender}
              </Button>
            ))}
          </div>
        </div>

        {/* Interests Text Input */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-2 block">Interests, Personality, Needs</label>
          <Textarea
            placeholder="e.g., cat lover, enjoys cooking, needs relaxation"
            value={interests}
            onChange={(e) => setInterests(e.target.value)}
            className="min-h-[80px] resize-none"
          />
        </div>

        {/* Suggested Tags */}
        <div>
          <label className="text-sm font-medium text-gray-700 mb-3 block">
            Quick Tags <span className="text-gray-400">(Tap to select)</span>
          </label>
          <div className="flex flex-wrap gap-2">
            {suggestedTags.map((tag) => (
              <button
                key={tag}
                onClick={() => toggleTag(tag)}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedTags.includes(tag)
                    ? "bg-orange-500 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full h-12 bg-orange-500 hover:bg-orange-600 text-white">Generate Recommendations</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Back
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
