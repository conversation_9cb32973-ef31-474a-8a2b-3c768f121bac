"use client"

import { Input } from "@/components/ui/input"
import { ArrowLeft, X } from "lucide-react"
import { useState } from "react"

export default function MobileScreen2_4() {
  const [searchQuery, setSearchQuery] = useState("")

  const recentSearches = ["wireless headphones", "coffee mug", "birthday gift", "tech gadgets"]

  const suggestedKeywords = ["Electronics", "Fashion", "Books", "Home", "Sports", "Beauty", "Toys", "Art"]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Search Header */}
      <div className="flex items-center gap-3 p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <div className="flex-1 relative">
          <Input
            type="text"
            placeholder="Search gifts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-10 pr-10 bg-gray-50 border-0 focus:bg-white focus:ring-2 focus:ring-orange-500"
            autoFocus
          />
          {searchQuery && (
            <button onClick={() => setSearchQuery("")} className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <X className="w-4 h-4 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Search Content */}
      <div className="flex-1 px-4 py-4">
        {/* Recent Searches */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Searches</h3>
          <div className="space-y-2">
            {recentSearches.map((search, index) => (
              <div key={index} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-xs text-gray-600">🔍</span>
                </div>
                <span className="text-gray-700">{search}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Suggested Keywords */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Suggested Categories</h3>
          <div className="flex flex-wrap gap-2">
            {suggestedKeywords.map((keyword, index) => (
              <button
                key={index}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
              >
                {keyword}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Dimmed Background Overlay */}
      <div className="absolute inset-0 bg-black/10 pointer-events-none"></div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
