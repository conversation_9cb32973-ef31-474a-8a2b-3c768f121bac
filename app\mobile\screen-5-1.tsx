import { Loader2 } from "lucide-react"

export default function MobileScreen5_1() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/30 z-10"></div>

      {/* Loading Overlay */}
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <div className="bg-white rounded-2xl p-8 mx-6 shadow-2xl">
          {/* Loading Animation */}
          <div className="flex justify-center mb-6">
            <Loader2 className="w-12 h-12 text-orange-500 animate-spin" />
          </div>

          {/* Title */}
          <h2 className="text-lg font-semibold text-center text-gray-900 mb-3">Redirecting to Store</h2>

          {/* Description */}
          <p className="text-gray-600 text-center text-sm">
            Taking you to our partner store to complete your purchase...
          </p>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
