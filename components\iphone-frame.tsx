import type React from "react"

interface IPhoneFrameProps {
  children: React.ReactNode
  className?: string
}

export function IPhoneFrame({ children, className = "" }: IPhoneFrameProps) {
  return (
    <div className={`flex items-center justify-center min-h-screen bg-gray-100 p-8 ${className}`}>
      {/* iPhone 16 Pro Max Frame */}
      <div className="relative">
        {/* Phone Body - Titanium Effect */}
        <div
          className="relative bg-gradient-to-br from-gray-300 via-gray-200 to-gray-400 rounded-[3rem] p-2 shadow-2xl"
          style={{
            width: "430px",
            height: "932px",
            background: "linear-gradient(145deg, #e8e8e8, #d1d1d1, #c0c0c0)",
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.1)",
          }}
        >
          {/* Screen Container */}
          <div
            className="relative bg-black rounded-[2.5rem] overflow-hidden"
            style={{
              width: "414px",
              height: "916px",
              background: "linear-gradient(145deg, #000000, #1a1a1a)",
            }}
          >
            {/* OLED Screen */}
            <div
              className="relative w-full h-full bg-white overflow-hidden"
              style={{
                borderRadius: "2.5rem",
                background: "linear-gradient(145deg, #ffffff, #f8f8f8)",
              }}
            >
              {/* Dynamic Island */}
              <div
                className="absolute top-6 left-1/2 transform -translate-x-1/2 bg-black rounded-full z-50"
                style={{
                  width: "126px",
                  height: "37px",
                  background: "linear-gradient(145deg, #000000, #1a1a1a)",
                  boxShadow: "inset 0 2px 4px rgba(0, 0, 0, 0.3)",
                }}
              >
                {/* Dynamic Island Content */}
                <div className="flex items-center justify-center h-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                  <div className="text-white text-xs font-medium">GiftSpark</div>
                </div>
              </div>

              {/* Status Bar */}
              <div className="absolute top-0 left-0 right-0 h-14 flex items-center justify-between px-8 pt-4 z-40">
                {/* Left side - Time */}
                <div className="text-black font-semibold text-lg">9:41</div>

                {/* Right side - Status icons */}
                <div className="flex items-center space-x-1">
                  {/* Signal */}
                  <div className="flex space-x-1">
                    <div className="w-1 h-2 bg-black rounded-full"></div>
                    <div className="w-1 h-3 bg-black rounded-full"></div>
                    <div className="w-1 h-4 bg-black rounded-full"></div>
                    <div className="w-1 h-4 bg-gray-300 rounded-full"></div>
                  </div>

                  {/* WiFi */}
                  <svg className="w-4 h-4 text-black ml-1" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.24 0 1 1 0 01-1.415-1.414 5 5 0 017.07 0 1 1 0 01-1.415 1.414zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>

                  {/* Battery */}
                  <div className="flex items-center ml-1">
                    <div className="w-6 h-3 border border-black rounded-sm relative">
                      <div className="w-4 h-1.5 bg-black rounded-sm absolute top-0.5 left-0.5"></div>
                    </div>
                    <div className="w-0.5 h-1.5 bg-black rounded-r-sm ml-0.5"></div>
                  </div>
                </div>
              </div>

              {/* Content Area */}
              <div
                className="absolute inset-0 overflow-hidden"
                style={{
                  top: "59px",
                  bottom: "50px",
                  borderRadius: "2.5rem",
                }}
              >
                <div className="w-full h-full overflow-y-auto">{children}</div>
              </div>

              {/* Home Indicator */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-40">
                <div
                  className="bg-black rounded-full opacity-60"
                  style={{
                    width: "134px",
                    height: "5px",
                  }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* Side Buttons */}
        {/* Volume Buttons */}
        <div className="absolute left-0 top-32 w-1 h-12 bg-gray-400 rounded-r-sm"></div>
        <div className="absolute left-0 top-48 w-1 h-12 bg-gray-400 rounded-r-sm"></div>

        {/* Power Button */}
        <div className="absolute right-0 top-40 w-1 h-16 bg-gray-400 rounded-l-sm"></div>

        {/* Action Button */}
        <div className="absolute left-0 top-20 w-1 h-8 bg-gray-400 rounded-r-sm"></div>
      </div>
    </div>
  )
}
