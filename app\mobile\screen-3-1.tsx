import { But<PERSON> } from "@/components/ui/button"
import { X, Shield, Users, Sparkles } from "lucide-react"

export default function MobileScreen3_1() {
  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Background Overlay */}
      <div className="absolute inset-0 bg-black/50 z-10"></div>

      {/* Modal Content */}
      <div className="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl z-20 p-6">
        {/* Close Button */}
        <div className="flex justify-end mb-4">
          <button className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Icon */}
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center">
            <Users className="w-8 h-8 text-white" />
          </div>
        </div>

        {/* Title */}
        <h2 className="text-xl font-bold text-center text-gray-900 mb-4">Connect Your Social Profile</h2>

        {/* Description */}
        <p className="text-gray-600 text-center mb-6 leading-relaxed">
          Let GiftSpark AI analyze your social media to understand your friend's interests and preferences for even
          better gift recommendations.
        </p>

        {/* Benefits */}
        <div className="space-y-3 mb-8">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-green-600" />
            </div>
            <span className="text-sm text-gray-700">More personalized recommendations</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <Shield className="w-4 h-4 text-green-600" />
            </div>
            <span className="text-sm text-gray-700">Your data is encrypted and secure</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-green-600" />
            </div>
            <span className="text-sm text-gray-700">Only public information is analyzed</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white">Connect Instagram</Button>
          <Button variant="outline" className="w-full h-12">
            Connect Facebook
          </Button>
          <Button variant="ghost" className="w-full text-gray-500">
            Skip for now
          </Button>
        </div>

        {/* Privacy Note */}
        <p className="text-xs text-gray-500 text-center mt-4">
          By connecting, you agree to our <span className="text-blue-600 underline">Privacy Policy</span> and{" "}
          <span className="text-blue-600 underline">Data Usage Terms</span>
        </p>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full z-30"></div>
    </div>
  )
}
