"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  Rocket,
  Globe,
  Server,
  Settings,
  CheckCircle,
  AlertTriangle,
  Clock,
  GitBranch,
  Code,
  Terminal,
  Upload,
  Download,
  Play,
  Pause,
} from "lucide-react"

export default function DeploymentManagementScreen() {
  const [deploymentInProgress, setDeploymentInProgress] = useState(false)
  const [deploymentProgress, setDeploymentProgress] = useState(0)

  const environments = [
    {
      name: "Production",
      url: "https://giftspark.com",
      status: "active",
      version: "v1.2.3",
      lastDeploy: "2024-01-14 18:30:00",
      health: "healthy",
    },
    {
      name: "Staging",
      url: "https://staging.giftspark.com",
      status: "active",
      version: "v1.2.4-beta",
      lastDeploy: "2024-01-15 14:20:00",
      health: "healthy",
    },
    {
      name: "Development",
      url: "https://dev.giftspark.com",
      status: "active",
      version: "v1.3.0-dev",
      lastDeploy: "2024-01-15 16:45:00",
      health: "warning",
    },
    {
      name: "Testing",
      url: "https://test.giftspark.com",
      status: "inactive",
      version: "v1.2.2",
      lastDeploy: "2024-01-10 09:15:00",
      health: "offline",
    },
  ]

  const deploymentHistory = [
    {
      id: 1,
      version: "v1.2.4-beta",
      environment: "Staging",
      status: "success",
      startTime: "2024-01-15 14:20:00",
      duration: "3m 45s",
      deployedBy: "<EMAIL>",
      branch: "develop",
      commit: "a1b2c3d",
    },
    {
      id: 2,
      version: "v1.2.3",
      environment: "Production",
      status: "success",
      startTime: "2024-01-14 18:30:00",
      duration: "5m 12s",
      deployedBy: "<EMAIL>",
      branch: "main",
      commit: "x9y8z7w",
    },
    {
      id: 3,
      version: "v1.2.3-rc1",
      environment: "Staging",
      status: "failed",
      startTime: "2024-01-14 15:45:00",
      duration: "2m 30s",
      deployedBy: "<EMAIL>",
      branch: "release/1.2.3",
      commit: "m5n4o3p",
    },
  ]

  const deploymentConfig = {
    autoDeployment: true,
    rollbackEnabled: true,
    healthChecks: true,
    notifications: true,
  }

  const handleDeploy = () => {
    setDeploymentInProgress(true)
    setDeploymentProgress(0)

    // Simulate deployment progress
    const interval = setInterval(() => {
      setDeploymentProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setDeploymentInProgress(false)
          return 100
        }
        return prev + 10
      })
    }, 800)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Deployment Management</h1>
          <p className="text-gray-600">Manage application deployments and environments</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Logs
          </Button>
          <Button onClick={handleDeploy} disabled={deploymentInProgress}>
            {deploymentInProgress ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Deploying...
              </>
            ) : (
              <>
                <Rocket className="h-4 w-4 mr-2" />
                Quick Deploy
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="environments" className="space-y-6">
        <TabsList>
          <TabsTrigger value="environments">Environments</TabsTrigger>
          <TabsTrigger value="deploy">Deploy</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="pipelines">CI/CD Pipelines</TabsTrigger>
        </TabsList>

        {/* Environments Tab */}
        <TabsContent value="environments">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {environments.map((env) => (
              <Card key={env.name}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Server className="h-5 w-5" />
                      {env.name}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Badge variant={env.status === "active" ? "default" : "secondary"}>{env.status}</Badge>
                      <Badge
                        variant={
                          env.health === "healthy" ? "default" : env.health === "warning" ? "secondary" : "destructive"
                        }
                      >
                        {env.health}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>
                    <a
                      href={env.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline flex items-center gap-1"
                    >
                      <Globe className="h-3 w-3" />
                      {env.url}
                    </a>
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Version:</span>
                      <p className="font-medium">{env.version}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">Last Deploy:</span>
                      <p className="font-medium">{env.lastDeploy}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="h-4 w-4 mr-2" />
                      Deploy
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure
                    </Button>
                    <Button variant="outline" size="sm">
                      <Terminal className="h-4 w-4 mr-2" />
                      Logs
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Deploy Tab */}
        <TabsContent value="deploy">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Rocket className="h-5 w-5" />
                  New Deployment
                </CardTitle>
                <CardDescription>Configure and start a new deployment</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="target-env">Target Environment</Label>
                  <select id="target-env" className="w-full px-3 py-2 border rounded-md">
                    <option value="staging">Staging</option>
                    <option value="production">Production</option>
                    <option value="development">Development</option>
                    <option value="testing">Testing</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="branch">Source Branch</Label>
                  <select id="branch" className="w-full px-3 py-2 border rounded-md">
                    <option value="main">main</option>
                    <option value="develop">develop</option>
                    <option value="release/1.2.4">release/1.2.4</option>
                    <option value="hotfix/critical-fix">hotfix/critical-fix</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="version">Version Tag</Label>
                  <Input id="version" placeholder="v1.2.4" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="deploy-notes">Deployment Notes</Label>
                  <Textarea id="deploy-notes" placeholder="Describe the changes in this deployment..." rows={3} />
                </div>

                <div className="space-y-2">
                  <Label>Deployment Options</Label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Run database migrations</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Clear application cache</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" />
                      <span className="text-sm">Send deployment notifications</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" defaultChecked />
                      <span className="text-sm">Enable rollback on failure</span>
                    </label>
                  </div>
                </div>

                <Button className="w-full" onClick={handleDeploy} disabled={deploymentInProgress}>
                  {deploymentInProgress ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Deploying...
                    </>
                  ) : (
                    <>
                      <Rocket className="h-4 w-4 mr-2" />
                      Start Deployment
                    </>
                  )}
                </Button>

                {deploymentInProgress && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Deployment Progress</span>
                      <span>{deploymentProgress}%</span>
                    </div>
                    <Progress value={deploymentProgress} />
                    <p className="text-sm text-gray-500">
                      {deploymentProgress < 20
                        ? "Preparing deployment..."
                        : deploymentProgress < 40
                          ? "Building application..."
                          : deploymentProgress < 60
                            ? "Running tests..."
                            : deploymentProgress < 80
                              ? "Deploying to server..."
                              : deploymentProgress < 100
                                ? "Running health checks..."
                                : "Deployment completed!"}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pre-deployment Checklist</CardTitle>
                <CardDescription>Verify these items before deploying</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>All tests passing</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Code review completed</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Database migrations tested</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <span>Environment variables updated</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Backup created</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span>Rollback plan ready</span>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Deployment Impact</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Estimated downtime: 2-3 minutes</li>
                    <li>• Database changes: Yes (2 migrations)</li>
                    <li>• Cache clear required: Yes</li>
                    <li>• API breaking changes: No</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Deployment History</CardTitle>
              <CardDescription>Recent deployment activities and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {deploymentHistory.map((deployment) => (
                  <div key={deployment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          deployment.status === "success"
                            ? "bg-green-500"
                            : deployment.status === "failed"
                              ? "bg-red-500"
                              : "bg-yellow-500"
                        }`}
                      />
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{deployment.version}</h4>
                          <Badge variant="outline">{deployment.environment}</Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {deployment.startTime}
                          </span>
                          <span>Duration: {deployment.duration}</span>
                          <span className="flex items-center gap-1">
                            <GitBranch className="h-3 w-3" />
                            {deployment.branch}
                          </span>
                          <span className="flex items-center gap-1">
                            <Code className="h-3 w-3" />
                            {deployment.commit}
                          </span>
                        </div>
                        <p className="text-sm text-gray-400 mt-1">Deployed by {deployment.deployedBy}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          deployment.status === "success"
                            ? "default"
                            : deployment.status === "failed"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {deployment.status}
                      </Badge>
                      <div className="flex gap-1">
                        <Button variant="outline" size="sm">
                          View Logs
                        </Button>
                        {deployment.status === "success" && (
                          <Button variant="outline" size="sm">
                            Rollback
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Configuration Tab */}
        <TabsContent value="config">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Deployment Settings</CardTitle>
                <CardDescription>Configure global deployment preferences</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Auto Deployment</p>
                      <p className="text-sm text-gray-500">Automatically deploy on push to main branch</p>
                    </div>
                    <input type="checkbox" defaultChecked={deploymentConfig.autoDeployment} />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Rollback Protection</p>
                      <p className="text-sm text-gray-500">Enable automatic rollback on deployment failure</p>
                    </div>
                    <input type="checkbox" defaultChecked={deploymentConfig.rollbackEnabled} />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Health Checks</p>
                      <p className="text-sm text-gray-500">Run health checks after deployment</p>
                    </div>
                    <input type="checkbox" defaultChecked={deploymentConfig.healthChecks} />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Deployment Notifications</p>
                      <p className="text-sm text-gray-500">Send notifications on deployment events</p>
                    </div>
                    <input type="checkbox" defaultChecked={deploymentConfig.notifications} />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="timeout">Deployment Timeout (minutes)</Label>
                  <Input id="timeout" type="number" defaultValue="15" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="concurrent">Max Concurrent Deployments</Label>
                  <Input id="concurrent" type="number" defaultValue="2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Environment Variables</CardTitle>
                <CardDescription>Manage environment-specific configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="env-select">Environment</Label>
                  <select id="env-select" className="w-full px-3 py-2 border rounded-md">
                    <option value="production">Production</option>
                    <option value="staging">Staging</option>
                    <option value="development">Development</option>
                  </select>
                </div>

                <div className="space-y-2 max-h-64 overflow-y-auto">
                  <div className="flex items-center justify-between p-2 border rounded">
                    <span className="font-mono text-sm">DATABASE_URL</span>
                    <Badge variant="outline">Set</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <span className="font-mono text-sm">REDIS_URL</span>
                    <Badge variant="outline">Set</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <span className="font-mono text-sm">JWT_SECRET</span>
                    <Badge variant="outline">Set</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <span className="font-mono text-sm">SMTP_HOST</span>
                    <Badge variant="secondary">Missing</Badge>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Upload className="h-4 w-4 mr-2" />
                    Import
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Add Variable
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* CI/CD Pipelines Tab */}
        <TabsContent value="pipelines">
          <Card>
            <CardHeader>
              <CardTitle>CI/CD Pipelines</CardTitle>
              <CardDescription>Manage continuous integration and deployment pipelines</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Build Pipeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Code Checkout</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Dependencies Install</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Code Compilation</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Asset Generation</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Test Pipeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Unit Tests</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Integration Tests</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm">E2E Tests</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Security Scan</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Deploy Pipeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Image Build</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Registry Push</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Server Deploy</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Health Check</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-4">Pipeline Configuration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="build-trigger">Build Trigger</Label>
                        <select id="build-trigger" className="w-full px-3 py-2 border rounded-md">
                          <option value="push">On Push</option>
                          <option value="pr">On Pull Request</option>
                          <option value="manual">Manual Only</option>
                          <option value="schedule">Scheduled</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="target-branches">Target Branches</Label>
                        <Input id="target-branches" defaultValue="main, develop" />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="node-version">Node.js Version</Label>
                        <Input id="node-version" defaultValue="18.x" />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="timeout">Pipeline Timeout (minutes)</Label>
                        <Input id="timeout" type="number" defaultValue="30" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
