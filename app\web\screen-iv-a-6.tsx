import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Package,
  Upload,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  BarChart3,
  FolderSyncIcon as Sync,
  T<PERSON>dingU<PERSON>,
  <PERSON>ting<PERSON>,
  HelpCircle,
  Bell,
  User,
} from "lucide-react"

export default function WebScreenIVA6() {
  const feedHistory = [
    {
      id: "F001",
      fileName: "products_feed_2024_12_15.xml",
      uploadDate: "2024-12-15 14:30",
      status: "Success",
      products: 247,
      errors: 0,
    },
    {
      id: "F002",
      fileName: "products_feed_2024_12_14.xml",
      uploadDate: "2024-12-14 09:15",
      status: "Success",
      products: 245,
      errors: 0,
    },
    {
      id: "F003",
      fileName: "products_feed_2024_12_13.xml",
      uploadDate: "2024-12-13 16:45",
      status: "Warning",
      products: 243,
      errors: 3,
    },
    {
      id: "F004",
      fileName: "products_feed_2024_12_12.xml",
      uploadDate: "2024-12-12 11:20",
      status: "Failed",
      products: 0,
      errors: 15,
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-semibold text-gray-900">Feed Management</h1>
            <div className="flex items-center gap-4">
              <Bell className="w-5 h-5 text-gray-600" />
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <span className="text-sm font-medium">TechSound Inc.</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className="w-64 bg-white border-r border-gray-200 min-h-screen">
          <nav className="p-4 space-y-2">
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
              <BarChart3 className="w-5 h-5" />
              Dashboard
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
              <Package className="w-5 h-5" />
              Product Management
            </a>
            <a
              href="#"
              className="flex items-center gap-3 px-3 py-2 bg-orange-50 text-orange-700 rounded-lg font-medium"
            >
              <Sync className="w-5 h-5" />
              Feed Management
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
              <TrendingUp className="w-5 h-5" />
              Performance Analytics
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
              <Settings className="w-5 h-5" />
              Account Settings
            </a>
            <a href="#" className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg">
              <HelpCircle className="w-5 h-5" />
              Help/Support
            </a>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Product Feed Management</h2>
              <p className="text-gray-600 mt-1">Upload and manage your product data feeds</p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Download Template
              </Button>
              <Button className="bg-orange-600 hover:bg-orange-700">
                <Upload className="w-4 h-4 mr-2" />
                Upload Feed
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Current Feed Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  Current Feed Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Last Update</span>
                    <span className="font-medium">Dec 15, 2024</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Products</span>
                    <span className="font-medium">247</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status</span>
                    <span className="text-green-600 font-medium">Healthy</span>
                  </div>
                  <Button variant="outline" className="w-full mt-4">
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Sync Now
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Feed Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Feed Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Uploads</span>
                    <span className="font-medium">24</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Success Rate</span>
                    <span className="font-medium text-green-600">95.8%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg. Processing Time</span>
                    <span className="font-medium">2.3 min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Next Auto Sync</span>
                    <span className="font-medium">Tomorrow 9:00 AM</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Upload New Feed */}
            <Card>
              <CardHeader>
                <CardTitle>Upload New Feed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="feed-type">Feed Type</Label>
                    <Select defaultValue="xml">
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="xml">XML Feed</SelectItem>
                        <SelectItem value="csv">CSV Feed</SelectItem>
                        <SelectItem value="json">JSON Feed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600 mb-2">Drop your feed file here</p>
                    <Button variant="outline" size="sm">
                      Browse Files
                    </Button>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="auto-sync" className="rounded" />
                    <Label htmlFor="auto-sync" className="text-sm">
                      Enable auto-sync
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Feed History */}
          <Card>
            <CardHeader>
              <CardTitle>Feed Upload History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-600">File Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Upload Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-600">Status</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-600">Products</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-600">Errors</th>
                      <th className="text-right py-3 px-4 font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {feedHistory.map((feed) => (
                      <tr key={feed.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4 text-gray-400" />
                            <span className="font-medium">{feed.fileName}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-gray-600">{feed.uploadDate}</td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            {feed.status === "Success" && <CheckCircle className="w-4 h-4 text-green-500" />}
                            {feed.status === "Warning" && <AlertCircle className="w-4 h-4 text-yellow-500" />}
                            {feed.status === "Failed" && <AlertCircle className="w-4 h-4 text-red-500" />}
                            {feed.status === "Processing" && <Clock className="w-4 h-4 text-blue-500" />}
                            <span
                              className={`text-sm font-medium ${
                                feed.status === "Success"
                                  ? "text-green-600"
                                  : feed.status === "Warning"
                                    ? "text-yellow-600"
                                    : feed.status === "Failed"
                                      ? "text-red-600"
                                      : "text-blue-600"
                              }`}
                            >
                              {feed.status}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4 text-right">{feed.products}</td>
                        <td className="py-3 px-4 text-right">
                          {feed.errors > 0 ? (
                            <span className="text-red-600 font-medium">{feed.errors}</span>
                          ) : (
                            <span className="text-gray-600">{feed.errors}</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center justify-end gap-2">
                            <Button size="sm" variant="ghost">
                              View Log
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
