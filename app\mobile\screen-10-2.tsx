"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, ChevronRight, ImageIcon, Palette, Type, Layout, Sparkles } from "lucide-react"
import { useState } from "react"

export default function MobileScreen10_2() {
  const [selectedTemplate, setSelectedTemplate] = useState(1)

  const templates = [
    { id: 1, name: "Birthday Balloons", image: "/placeholder.svg?height=120&width=120&text=Template+1" },
    { id: 2, name: "Elegant Thank You", image: "/placeholder.svg?height=120&width=120&text=Template+2" },
    { id: 3, name: "Celebration", image: "/placeholder.svg?height=120&width=120&text=Template+3" },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Create E-Card</h1>
        <div className="w-6"></div>
      </div>

      {/* Progress Steps */}
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <div className="w-8 h-2 bg-orange-500 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
          <div className="w-8 h-2 bg-gray-200 rounded-full"></div>
        </div>
        <p className="text-xs text-gray-500 mt-1">Step 1: Choose Template</p>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 py-4 pb-24 overflow-y-auto">
        {/* Template Preview */}
        <div className="mb-6">
          <div className="bg-gray-50 rounded-xl p-4 flex justify-center">
            <div className="w-48 h-64 bg-white rounded-lg shadow-md overflow-hidden">
              <ImageIcon
                src={templates.find((t) => t.id === selectedTemplate)?.image || "/placeholder.svg"}
                alt="Card Template"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* Template Selection */}
        <div className="mb-6">
          <h2 className="text-sm font-medium text-gray-700 mb-3">Select Template</h2>
          <div className="flex gap-3 overflow-x-auto pb-2">
            {templates.map((template) => (
              <div
                key={template.id}
                className={`flex-shrink-0 w-24 h-32 rounded-lg overflow-hidden border-2 ${
                  selectedTemplate === template.id ? "border-orange-500" : "border-transparent"
                }`}
                onClick={() => setSelectedTemplate(template.id)}
              >
                <ImageIcon
                  src={template.image || "/placeholder.svg"}
                  alt={template.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Customization Options */}
        <div className="mb-6">
          <h2 className="text-sm font-medium text-gray-700 mb-3">Customization Options</h2>
          <div className="space-y-2">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <Layout className="w-4 h-4 text-orange-500" />
                </div>
                <span className="text-gray-900">Layout Options</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <Palette className="w-4 h-4 text-orange-500" />
                </div>
                <span className="text-gray-900">Color Themes</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <Type className="w-4 h-4 text-orange-500" />
                </div>
                <span className="text-gray-900">Font Styles</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                  <ImageIcon className="w-4 h-4 text-orange-500" />
                </div>
                <span className="text-gray-900">Add Images</span>
              </div>
              <ChevronRight className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* AI Assistance */}
        <div className="bg-blue-50 rounded-xl p-4 border border-blue-100 mb-6">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900 mb-1">AI Message Writer</h3>
              <p className="text-sm text-blue-800 mb-3">
                Need help writing the perfect message? Let our AI help you craft a personalized message.
              </p>
              <Button size="sm" variant="outline" className="bg-white text-blue-600 border-blue-200">
                Generate Message
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="absolute bottom-8 left-0 right-0 px-4 space-y-3">
        <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">Continue to Message</Button>
        <Button variant="ghost" className="w-full text-gray-500">
          Cancel
        </Button>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
