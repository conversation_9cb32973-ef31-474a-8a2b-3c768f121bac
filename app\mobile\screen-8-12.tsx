import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ArrowLeft, Send, Search, Book, MessageCircle, Phone } from "lucide-react"

export default function MobileScreen8_12() {
  const helpTopics = [
    { title: "Getting Started", icon: "🚀" },
    { title: "Gift Recommendations", icon: "🎁" },
    { title: "Account & Profile", icon: "👤" },
    { title: "Payment & Billing", icon: "💳" },
    { title: "Technical Issues", icon: "🔧" },
    { title: "Privacy & Security", icon: "🔒" },
  ]

  const faqItems = [
    {
      question: "How does AI gift recommendation work?",
      category: "Gift Recommendations",
    },
    {
      question: "How to add important dates?",
      category: "Getting Started",
    },
    {
      question: "Can I change my email address?",
      category: "Account & Profile",
    },
  ]

  return (
    <div className="w-[430px] h-[932px] bg-white mx-auto rounded-[40px] overflow-hidden relative">
      {/* iOS Status Bar */}
      <div className="h-12 bg-black/5 flex items-center justify-between px-6 text-sm font-medium">
        <span>9:41</span>
        <div className="flex items-center gap-1">
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-4 h-2 bg-black/60 rounded-sm"></div>
          <div className="w-6 h-3 bg-black/60 rounded-sm"></div>
        </div>
      </div>

      {/* Top Bar */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <ArrowLeft className="w-6 h-6 text-gray-600" />
        <h1 className="text-lg font-semibold">Help & Support</h1>
        <div className="w-6"></div>
      </div>

      {/* Search Bar */}
      <div className="px-4 py-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input placeholder="Search help articles..." className="pl-10 h-12 bg-gray-50 border-0" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 px-4 pb-24 overflow-y-auto">
        {/* Quick Actions */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-3 gap-3">
            <div className="flex flex-col items-center gap-2 p-4 bg-blue-50 rounded-xl">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-blue-900">Live Chat</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 bg-green-50 rounded-xl">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <Phone className="w-5 h-5 text-green-600" />
              </div>
              <span className="text-sm font-medium text-green-900">Call Us</span>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 bg-purple-50 rounded-xl">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <Book className="w-5 h-5 text-purple-600" />
              </div>
              <span className="text-sm font-medium text-purple-900">Guide</span>
            </div>
          </div>
        </div>

        {/* Help Topics */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Help Topics</h2>
          <div className="grid grid-cols-2 gap-3">
            {helpTopics.map((topic, index) => (
              <div key={index} className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl hover:bg-gray-100">
                <span className="text-2xl">{topic.icon}</span>
                <span className="font-medium text-gray-900 text-sm">{topic.title}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Popular FAQs */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Popular FAQs</h2>
          <div className="space-y-3">
            {faqItems.map((faq, index) => (
              <div key={index} className="p-4 bg-white border border-gray-200 rounded-xl">
                <h3 className="font-medium text-gray-900 mb-1">{faq.question}</h3>
                <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full">{faq.category}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Contact Form */}
        <div>
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Contact Support</h2>
          <div className="space-y-4">
            <Select>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Select issue type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="technical">Technical Issue</SelectItem>
                <SelectItem value="account">Account Problem</SelectItem>
                <SelectItem value="billing">Billing Question</SelectItem>
                <SelectItem value="feature">Feature Request</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Input placeholder="Subject" className="h-12" />

            <Textarea placeholder="Describe your issue in detail..." className="min-h-[100px] resize-none" />

            <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
              <Send className="w-4 h-4 mr-2" />
              Send Message
            </Button>
          </div>
        </div>
      </div>

      {/* iOS Home Indicator */}
      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-black/30 rounded-full"></div>
    </div>
  )
}
